/*
 * Copyright (c) 2023, Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, IN<PERSON>DENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/*
 *  ============ ti_msp_dl_config.h =============
 *  Configured MSPM0 DriverLib module declarations
 *
 *  DO NOT EDIT - This file is generated for the MSPM0G350X
 *  by the SysConfig tool.
 */
#ifndef ti_msp_dl_config_h
#define ti_msp_dl_config_h

#define CONFIG_MSPM0G350X
#define CONFIG_MSPM0G3507

#if defined(__ti_version__) || defined(__TI_COMPILER_VERSION__)
#define SYSCONFIG_WEAK __attribute__((weak))
#elif defined(__IAR_SYSTEMS_ICC__)
#define SYSCONFIG_WEAK __weak
#elif defined(__GNUC__)
#define SYSCONFIG_WEAK __attribute__((weak))
#endif

#include <ti/devices/msp/msp.h>
#include <ti/driverlib/driverlib.h>
#include <ti/driverlib/m0p/dl_core.h>

#ifdef __cplusplus
extern "C" {
#endif

/*
 *  ======== SYSCFG_DL_init ========
 *  Perform all required MSP DL initialization
 *
 *  This function should be called once at a point before any use of
 *  MSP DL.
 */


/* clang-format off */

#define POWER_STARTUP_DELAY                                                (16)



#define CPUCLK_FREQ                                                     80000000



/* Defines for Motor */
#define Motor_INST                                                         TIMG0
#define Motor_INST_IRQHandler                                   TIMG0_IRQHandler
#define Motor_INST_INT_IRQN                                     (TIMG0_INT_IRQn)
#define Motor_INST_CLK_FREQ                                               400000
/* GPIO defines for channel 0 */
#define GPIO_Motor_C0_PORT                                                 GPIOA
#define GPIO_Motor_C0_PIN                                         DL_GPIO_PIN_12
#define GPIO_Motor_C0_IOMUX                                      (IOMUX_PINCM34)
#define GPIO_Motor_C0_IOMUX_FUNC                     IOMUX_PINCM34_PF_TIMG0_CCP0
#define GPIO_Motor_C0_IDX                                    DL_TIMER_CC_0_INDEX
/* GPIO defines for channel 1 */
#define GPIO_Motor_C1_PORT                                                 GPIOA
#define GPIO_Motor_C1_PIN                                         DL_GPIO_PIN_13
#define GPIO_Motor_C1_IOMUX                                      (IOMUX_PINCM35)
#define GPIO_Motor_C1_IOMUX_FUNC                     IOMUX_PINCM35_PF_TIMG0_CCP1
#define GPIO_Motor_C1_IDX                                    DL_TIMER_CC_1_INDEX




/* Port definition for Pin Group Motor_direction */
#define Motor_direction_PORT                                             (GPIOB)

/* Defines for AIN1: GPIOB.0 with pinCMx 12 on package pin 47 */
#define Motor_direction_AIN1_PIN                                 (DL_GPIO_PIN_0)
#define Motor_direction_AIN1_IOMUX                               (IOMUX_PINCM12)
/* Defines for AIN2: GPIOB.3 with pinCMx 16 on package pin 51 */
#define Motor_direction_AIN2_PIN                                 (DL_GPIO_PIN_3)
#define Motor_direction_AIN2_IOMUX                               (IOMUX_PINCM16)
/* Defines for BIN1: GPIOB.1 with pinCMx 13 on package pin 48 */
#define Motor_direction_BIN1_PIN                                 (DL_GPIO_PIN_1)
#define Motor_direction_BIN1_IOMUX                               (IOMUX_PINCM13)
/* Defines for BIN2: GPIOB.2 with pinCMx 15 on package pin 50 */
#define Motor_direction_BIN2_PIN                                 (DL_GPIO_PIN_2)
#define Motor_direction_BIN2_IOMUX                               (IOMUX_PINCM15)
/* Port definition for Pin Group ECODER_R */
#define ECODER_R_PORT                                                    (GPIOB)

/* Defines for LA: GPIOB.21 with pinCMx 49 on package pin 20 */
// groups represented: ["ECODER_L","ECODER_R"]
// pins affected: ["RA","RB","LA","LB"]
#define GPIO_MULTIPLE_GPIOB_INT_IRQN                            (GPIOB_INT_IRQn)
#define GPIO_MULTIPLE_GPIOB_INT_IIDX            (DL_INTERRUPT_GROUP1_IIDX_GPIOB)
#define ECODER_R_LA_IIDX                                    (DL_GPIO_IIDX_DIO21)
#define ECODER_R_LA_PIN                                         (DL_GPIO_PIN_21)
#define ECODER_R_LA_IOMUX                                        (IOMUX_PINCM49)
/* Defines for LB: GPIOB.22 with pinCMx 50 on package pin 21 */
#define ECODER_R_LB_IIDX                                    (DL_GPIO_IIDX_DIO22)
#define ECODER_R_LB_PIN                                         (DL_GPIO_PIN_22)
#define ECODER_R_LB_IOMUX                                        (IOMUX_PINCM50)
/* Port definition for Pin Group ECODER_L */
#define ECODER_L_PORT                                                    (GPIOB)

/* Defines for RA: GPIOB.15 with pinCMx 32 on package pin 3 */
#define ECODER_L_RA_IIDX                                    (DL_GPIO_IIDX_DIO15)
#define ECODER_L_RA_PIN                                         (DL_GPIO_PIN_15)
#define ECODER_L_RA_IOMUX                                        (IOMUX_PINCM32)
/* Defines for RB: GPIOB.16 with pinCMx 33 on package pin 4 */
#define ECODER_L_RB_IIDX                                    (DL_GPIO_IIDX_DIO16)
#define ECODER_L_RB_PIN                                         (DL_GPIO_PIN_16)
#define ECODER_L_RB_IOMUX                                        (IOMUX_PINCM33)



/* clang-format on */

void SYSCFG_DL_init(void);
void SYSCFG_DL_initPower(void);
void SYSCFG_DL_GPIO_init(void);
void SYSCFG_DL_SYSCTL_init(void);
void SYSCFG_DL_Motor_init(void);

void SYSCFG_DL_SYSTICK_init(void);

bool SYSCFG_DL_saveConfiguration(void);
bool SYSCFG_DL_restoreConfiguration(void);

#ifdef __cplusplus
}
#endif

#endif /* ti_msp_dl_config_h */
