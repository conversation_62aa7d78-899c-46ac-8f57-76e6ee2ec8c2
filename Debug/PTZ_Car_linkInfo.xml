<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v4.0.3.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <command_line>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\bin\tiarmlnk -IC:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib -o PTZ_Car.out -mPTZ_Car.map -iC:/ti/mspm0_sdk_2_04_00_06/source -iC:/Users/<USER>/workspace_ccstheia/PTZ_Car -iC:/Users/<USER>/workspace_ccstheia/PTZ_Car/Debug/syscfg -iC:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib --diag_wrap=off --display_error_number --warn_sections --xml_link_info=PTZ_Car_linkInfo.xml --rom_model ./ti_msp_dl_config.o ./startup_mspm0g350x_ticlang.o ./main.o ./Hardware/Motor.o ./Hardware/pid.o ./Hardware/pid_app.o -l./device_linker.cmd -ldevice.cmd.genlibs -llibc.a --start-group -llibc++.a -llibc++abi.a -llibc.a -llibsys.a -llibsysbm.a -llibclang_rt.builtins.a -llibclang_rt.profile.a --end-group --cg_opt_level=2</command_line>
   <link_time>0x6889cf94</link_time>
   <link_errors>0x0</link_errors>
   <output_file>C:\Users\<USER>\workspace_ccstheia\PTZ_Car\Debug\PTZ_Car.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0x779</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>C:\Users\<USER>\workspace_ccstheia\PTZ_Car\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>C:\Users\<USER>\workspace_ccstheia\PTZ_Car\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>C:\Users\<USER>\workspace_ccstheia\PTZ_Car\Debug\.\</path>
         <kind>object</kind>
         <file>main.o</file>
         <name>main.o</name>
      </input_file>
      <input_file id="fl-4">
         <path>C:\Users\<USER>\workspace_ccstheia\PTZ_Car\Debug\.\Hardware\</path>
         <kind>object</kind>
         <file>Motor.o</file>
         <name>Motor.o</name>
      </input_file>
      <input_file id="fl-5">
         <path>C:\Users\<USER>\workspace_ccstheia\PTZ_Car\Debug\.\Hardware\</path>
         <kind>object</kind>
         <file>pid.o</file>
         <name>pid.o</name>
      </input_file>
      <input_file id="fl-6">
         <path>C:\Users\<USER>\workspace_ccstheia\PTZ_Car\Debug\.\Hardware\</path>
         <kind>object</kind>
         <file>pid_app.o</file>
         <name>pid_app.o</name>
      </input_file>
      <input_file id="fl-13">
         <path>C:\Users\<USER>\workspace_ccstheia\PTZ_Car\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-14">
         <path>C:\ti\mspm0_sdk_2_04_00_06\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-15">
         <path>C:\ti\mspm0_sdk_2_04_00_06\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_timer.o</name>
      </input_file>
      <input_file id="fl-16">
         <path>C:\ti\mspm0_sdk_2_04_00_06\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_sysctl_mspm0g1x0x_g3x0x.o</name>
      </input_file>
      <input_file id="fl-2d">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-2e">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-2f">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-30">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-31">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-32">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-33">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-34">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-35">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-36">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-37">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-d7">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostexit.c.obj</name>
      </input_file>
      <input_file id="fl-d8">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>addsf3.S.obj</name>
      </input_file>
      <input_file id="fl-d9">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>mulsf3.S.obj</name>
      </input_file>
      <input_file id="fl-da">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixsfsi.S.obj</name>
      </input_file>
      <input_file id="fl-db">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatsisf.S.obj</name>
      </input_file>
      <input_file id="fl-dc">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_fcmp.S.obj</name>
      </input_file>
      <input_file id="fl-dd">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-de">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-df">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>comparesf2.S.obj</name>
      </input_file>
      <input_file id="fl-e0">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldsi3.S.obj</name>
      </input_file>
      <input_file id="fl-e1">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-e2">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-e7">
         <name>.text.DL_Timer_initFourCCPWMMode</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0x104</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-de">
         <name>.text.DL_SYSCTL_configSYSPLL</name>
         <load_address>0x1c4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c4</run_address>
         <size>0xdc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-39">
         <name>.text.GROUP1_IRQHandler</name>
         <load_address>0x2a0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2a0</run_address>
         <size>0xbc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-80">
         <name>.text:memcpy</name>
         <load_address>0x35c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x35c</run_address>
         <size>0x9a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e1"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x3f6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x3f6</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-70">
         <name>.text.main</name>
         <load_address>0x3f8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x3f8</run_address>
         <size>0x98</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-b7">
         <name>.text.SYSCFG_DL_Motor_init</name>
         <load_address>0x490</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x490</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b4">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0x51c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x51c</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.text:decompress:lzss</name>
         <load_address>0x598</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x598</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-b5">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0x614</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x614</run_address>
         <size>0x6c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-e4">
         <name>.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK</name>
         <load_address>0x680</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x680</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-8a">
         <name>.text.PID_App_Init</name>
         <load_address>0x6c4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6c4</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-9a">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0x708</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x708</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-b3">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0x744</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x744</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-55">
         <name>.text:_c_int00_noargs</name>
         <load_address>0x778</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x778</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.text.Set_PWM_Duty</name>
         <load_address>0x7a0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7a0</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-e9">
         <name>.text.DL_Timer_setCaptCompUpdateMethod</name>
         <load_address>0x7c0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7c0</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-e6">
         <name>.text.DL_Timer_setClockConfig</name>
         <load_address>0x7dc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7dc</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-e8">
         <name>.text.DL_Timer_setCaptureCompareOutCtl</name>
         <load_address>0x7f8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7f8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-85">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0x810</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x810</run_address>
         <size>0x18</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-4c">
         <name>.text:decompress:ZI:__TI_zero_init_nomemset</name>
         <load_address>0x828</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x828</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-20">
         <name>.text:decompress:none</name>
         <load_address>0x83e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x83e</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-c0">
         <name>.text.DL_Timer_setCaptureCompareValue</name>
         <load_address>0x850</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x850</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-34">
         <name>.text.SysTick_Handler</name>
         <load_address>0x860</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x860</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-da">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0x870</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x870</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-b8">
         <name>.text.SYSCFG_DL_SYSTICK_init</name>
         <load_address>0x87a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x87a</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-45">
         <name>.text.__aeabi_memcpy</name>
         <load_address>0x87c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x87c</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-93">
         <name>.text:abort</name>
         <load_address>0x884</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x884</run_address>
         <size>0x6</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-c9">
         <name>.text.HOSTexit</name>
         <load_address>0x88a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x88a</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0x88e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x88e</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-6c">
         <name>.text._system_pre_init</name>
         <load_address>0x892</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x892</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-13d">
         <name>__TI_handler_table</name>
         <load_address>0x8d0</load_address>
         <readonly>true</readonly>
         <run_address>0x8d0</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-140">
         <name>.cinit..bss.load</name>
         <load_address>0x8dc</load_address>
         <readonly>true</readonly>
         <run_address>0x8dc</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-13f">
         <name>.cinit..data.load</name>
         <load_address>0x8e4</load_address>
         <readonly>true</readonly>
         <run_address>0x8e4</run_address>
         <size>0x7</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-13e">
         <name>__TI_cinit_table</name>
         <load_address>0x8ec</load_address>
         <readonly>true</readonly>
         <run_address>0x8ec</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-e5">
         <name>.rodata.gSYSPLLConfig</name>
         <load_address>0x898</load_address>
         <readonly>true</readonly>
         <run_address>0x898</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-eb">
         <name>.rodata.gMotorConfig</name>
         <load_address>0x8c0</load_address>
         <readonly>true</readonly>
         <run_address>0x8c0</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-ea">
         <name>.rodata.gMotorClockConfig</name>
         <load_address>0x8c8</load_address>
         <readonly>true</readonly>
         <run_address>0x8c8</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-107">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-62">
         <name>.data.gEncoderCount_R</name>
         <load_address>0x2020005c</load_address>
         <readwrite>true</readwrite>
         <run_address>0x2020005c</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-61">
         <name>.data.gEncoderCount_L</name>
         <load_address>0x20200058</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200058</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-5c">
         <name>.common:tick_ms</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200054</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-60">
         <name>.common:gpioB</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200050</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-bb">
         <name>.common:pid_left</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-bc">
         <name>.common:pid_right</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200028</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-142">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-b6">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x82</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.debug_loc</name>
         <load_address>0x82</load_address>
         <run_address>0x82</run_address>
         <size>0x71</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-c8">
         <name>.debug_loc</name>
         <load_address>0xf3</load_address>
         <run_address>0xf3</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-bf">
         <name>.debug_loc</name>
         <load_address>0x131</load_address>
         <run_address>0x131</run_address>
         <size>0x68</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-fb">
         <name>.debug_loc</name>
         <load_address>0x199</load_address>
         <run_address>0x199</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-c4">
         <name>.debug_loc</name>
         <load_address>0x1ac</load_address>
         <run_address>0x1ac</run_address>
         <size>0x1a27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-e2">
         <name>.debug_loc</name>
         <load_address>0x1bd3</load_address>
         <run_address>0x1bd3</run_address>
         <size>0x414</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_loc</name>
         <load_address>0x1fe7</load_address>
         <run_address>0x1fe7</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-9b">
         <name>.debug_loc</name>
         <load_address>0x20bf</load_address>
         <run_address>0x20bf</run_address>
         <size>0x424</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_loc</name>
         <load_address>0x24e3</load_address>
         <run_address>0x24e3</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.debug_loc</name>
         <load_address>0x264f</load_address>
         <run_address>0x264f</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-43">
         <name>.debug_loc</name>
         <load_address>0x26be</load_address>
         <run_address>0x26be</run_address>
         <size>0x167</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-99">
         <name>.debug_loc</name>
         <load_address>0x2825</load_address>
         <run_address>0x2825</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-b9">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1f5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_abbrev</name>
         <load_address>0x1f5</load_address>
         <run_address>0x1f5</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-5d">
         <name>.debug_abbrev</name>
         <load_address>0x262</load_address>
         <run_address>0x262</run_address>
         <size>0x1e4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-c6">
         <name>.debug_abbrev</name>
         <load_address>0x446</load_address>
         <run_address>0x446</run_address>
         <size>0xef</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-bd">
         <name>.debug_abbrev</name>
         <load_address>0x535</load_address>
         <run_address>0x535</run_address>
         <size>0x105</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-f9">
         <name>.debug_abbrev</name>
         <load_address>0x63a</load_address>
         <run_address>0x63a</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-ec">
         <name>.debug_abbrev</name>
         <load_address>0x69c</load_address>
         <run_address>0x69c</run_address>
         <size>0x286</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-fc">
         <name>.debug_abbrev</name>
         <load_address>0x922</load_address>
         <run_address>0x922</run_address>
         <size>0x218</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_abbrev</name>
         <load_address>0xb3a</load_address>
         <run_address>0xb3a</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-d3">
         <name>.debug_abbrev</name>
         <load_address>0xbe9</load_address>
         <run_address>0xbe9</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-83">
         <name>.debug_abbrev</name>
         <load_address>0xd59</load_address>
         <run_address>0xd59</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-65">
         <name>.debug_abbrev</name>
         <load_address>0xd92</load_address>
         <run_address>0xd92</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_abbrev</name>
         <load_address>0xe54</load_address>
         <run_address>0xe54</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_abbrev</name>
         <load_address>0xec4</load_address>
         <run_address>0xec4</run_address>
         <size>0x8d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.debug_abbrev</name>
         <load_address>0xf51</load_address>
         <run_address>0xf51</run_address>
         <size>0x98</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-ee">
         <name>.debug_abbrev</name>
         <load_address>0xfe9</load_address>
         <run_address>0xfe9</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-64">
         <name>.debug_abbrev</name>
         <load_address>0x1015</load_address>
         <run_address>0x1015</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-ac">
         <name>.debug_abbrev</name>
         <load_address>0x103c</load_address>
         <run_address>0x103c</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e1"/>
      </object_component>
      <object_component id="oc-144">
         <name>.debug_abbrev</name>
         <load_address>0x1061</load_address>
         <run_address>0x1061</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-86">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x238b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x238b</load_address>
         <run_address>0x238b</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_info</name>
         <load_address>0x240b</load_address>
         <run_address>0x240b</run_address>
         <size>0xd75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-90">
         <name>.debug_info</name>
         <load_address>0x3180</load_address>
         <run_address>0x3180</run_address>
         <size>0x737</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-8b">
         <name>.debug_info</name>
         <load_address>0x38b7</load_address>
         <run_address>0x38b7</run_address>
         <size>0x29c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-dd">
         <name>.debug_info</name>
         <load_address>0x3b53</load_address>
         <run_address>0x3b53</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-c5">
         <name>.debug_info</name>
         <load_address>0x3bc8</load_address>
         <run_address>0x3bc8</run_address>
         <size>0x3172</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-e3">
         <name>.debug_info</name>
         <load_address>0x6d3a</load_address>
         <run_address>0x6d3a</run_address>
         <size>0x1090</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0x7dca</load_address>
         <run_address>0x7dca</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-9f">
         <name>.debug_info</name>
         <load_address>0x81ed</load_address>
         <run_address>0x81ed</run_address>
         <size>0x744</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-6d">
         <name>.debug_info</name>
         <load_address>0x8931</load_address>
         <run_address>0x8931</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-4d">
         <name>.debug_info</name>
         <load_address>0x8977</load_address>
         <run_address>0x8977</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_info</name>
         <load_address>0x8b09</load_address>
         <run_address>0x8b09</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_info</name>
         <load_address>0x8bcf</load_address>
         <run_address>0x8bcf</run_address>
         <size>0x17c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-97">
         <name>.debug_info</name>
         <load_address>0x8d4b</load_address>
         <run_address>0x8d4b</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-cc">
         <name>.debug_info</name>
         <load_address>0x8e43</load_address>
         <run_address>0x8e43</run_address>
         <size>0x3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_info</name>
         <load_address>0x8e7e</load_address>
         <run_address>0x8e7e</run_address>
         <size>0x199</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-7d">
         <name>.debug_info</name>
         <load_address>0x9017</load_address>
         <run_address>0x9017</run_address>
         <size>0x2fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e1"/>
      </object_component>
      <object_component id="oc-143">
         <name>.debug_info</name>
         <load_address>0x9311</load_address>
         <run_address>0x9311</run_address>
         <size>0xa3</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-88">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x38</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_ranges</name>
         <load_address>0x38</load_address>
         <run_address>0x38</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_ranges</name>
         <load_address>0x50</load_address>
         <run_address>0x50</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-8d">
         <name>.debug_ranges</name>
         <load_address>0x98</load_address>
         <run_address>0x98</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-c2">
         <name>.debug_ranges</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-e0">
         <name>.debug_ranges</name>
         <load_address>0x298</load_address>
         <run_address>0x298</run_address>
         <size>0x1a8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.debug_ranges</name>
         <load_address>0x440</load_address>
         <run_address>0x440</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-9d">
         <name>.debug_ranges</name>
         <load_address>0x488</load_address>
         <run_address>0x488</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-50">
         <name>.debug_ranges</name>
         <load_address>0x4d0</load_address>
         <run_address>0x4d0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-44">
         <name>.debug_ranges</name>
         <load_address>0x4e8</load_address>
         <run_address>0x4e8</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-94">
         <name>.debug_ranges</name>
         <load_address>0x538</load_address>
         <run_address>0x538</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-7f">
         <name>.debug_ranges</name>
         <load_address>0x550</load_address>
         <run_address>0x550</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e1"/>
      </object_component>
      <object_component id="oc-ba">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1e0e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_str</name>
         <load_address>0x1e0e</load_address>
         <run_address>0x1e0e</run_address>
         <size>0x15a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-5e">
         <name>.debug_str</name>
         <load_address>0x1f68</load_address>
         <run_address>0x1f68</run_address>
         <size>0x839</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-c7">
         <name>.debug_str</name>
         <load_address>0x27a1</load_address>
         <run_address>0x27a1</run_address>
         <size>0x493</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-be">
         <name>.debug_str</name>
         <load_address>0x2c34</load_address>
         <run_address>0x2c34</run_address>
         <size>0x25a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-fa">
         <name>.debug_str</name>
         <load_address>0x2e8e</load_address>
         <run_address>0x2e8e</run_address>
         <size>0x16d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-ed">
         <name>.debug_str</name>
         <load_address>0x2ffb</load_address>
         <run_address>0x2ffb</run_address>
         <size>0x1dcb</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-fd">
         <name>.debug_str</name>
         <load_address>0x4dc6</load_address>
         <run_address>0x4dc6</run_address>
         <size>0x1075</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_str</name>
         <load_address>0x5e3b</load_address>
         <run_address>0x5e3b</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-d4">
         <name>.debug_str</name>
         <load_address>0x6060</load_address>
         <run_address>0x6060</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-84">
         <name>.debug_str</name>
         <load_address>0x638f</load_address>
         <run_address>0x638f</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-66">
         <name>.debug_str</name>
         <load_address>0x6484</load_address>
         <run_address>0x6484</run_address>
         <size>0x19b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_str</name>
         <load_address>0x661f</load_address>
         <run_address>0x661f</run_address>
         <size>0x168</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_str</name>
         <load_address>0x6787</load_address>
         <run_address>0x6787</run_address>
         <size>0x1d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-ce">
         <name>.debug_str</name>
         <load_address>0x695c</load_address>
         <run_address>0x695c</run_address>
         <size>0x148</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-ef">
         <name>.debug_str</name>
         <load_address>0x6aa4</load_address>
         <run_address>0x6aa4</run_address>
         <size>0xe9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-87">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x9c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_frame</name>
         <load_address>0x9c</load_address>
         <run_address>0x9c</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_frame</name>
         <load_address>0xcc</load_address>
         <run_address>0xcc</run_address>
         <size>0x68</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-91">
         <name>.debug_frame</name>
         <load_address>0x134</load_address>
         <run_address>0x134</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-8e">
         <name>.debug_frame</name>
         <load_address>0x15c</load_address>
         <run_address>0x15c</run_address>
         <size>0x68</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-db">
         <name>.debug_frame</name>
         <load_address>0x1c4</load_address>
         <run_address>0x1c4</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-c1">
         <name>.debug_frame</name>
         <load_address>0x1e4</load_address>
         <run_address>0x1e4</run_address>
         <size>0x408</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-df">
         <name>.debug_frame</name>
         <load_address>0x5ec</load_address>
         <run_address>0x5ec</run_address>
         <size>0x12c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-54">
         <name>.debug_frame</name>
         <load_address>0x718</load_address>
         <run_address>0x718</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-9c">
         <name>.debug_frame</name>
         <load_address>0x7a8</load_address>
         <run_address>0x7a8</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-6f">
         <name>.debug_frame</name>
         <load_address>0x8a8</load_address>
         <run_address>0x8a8</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-51">
         <name>.debug_frame</name>
         <load_address>0x8c8</load_address>
         <run_address>0x8c8</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_frame</name>
         <load_address>0x900</load_address>
         <run_address>0x900</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_frame</name>
         <load_address>0x928</load_address>
         <run_address>0x928</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-95">
         <name>.debug_frame</name>
         <load_address>0x958</load_address>
         <run_address>0x958</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-cb">
         <name>.debug_frame</name>
         <load_address>0x988</load_address>
         <run_address>0x988</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-89">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x50a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_line</name>
         <load_address>0x50a</load_address>
         <run_address>0x50a</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_line</name>
         <load_address>0x5c2</load_address>
         <run_address>0x5c2</run_address>
         <size>0x42d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-92">
         <name>.debug_line</name>
         <load_address>0x9ef</load_address>
         <run_address>0x9ef</run_address>
         <size>0x1e0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-8c">
         <name>.debug_line</name>
         <load_address>0xbcf</load_address>
         <run_address>0xbcf</run_address>
         <size>0x215</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-dc">
         <name>.debug_line</name>
         <load_address>0xde4</load_address>
         <run_address>0xde4</run_address>
         <size>0x178</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-c3">
         <name>.debug_line</name>
         <load_address>0xf5c</load_address>
         <run_address>0xf5c</run_address>
         <size>0x176e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-e1">
         <name>.debug_line</name>
         <load_address>0x26ca</load_address>
         <run_address>0x26ca</run_address>
         <size>0x982</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.debug_line</name>
         <load_address>0x304c</load_address>
         <run_address>0x304c</run_address>
         <size>0x1dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-9e">
         <name>.debug_line</name>
         <load_address>0x3228</load_address>
         <run_address>0x3228</run_address>
         <size>0x51a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-6e">
         <name>.debug_line</name>
         <load_address>0x3742</load_address>
         <run_address>0x3742</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.debug_line</name>
         <load_address>0x3780</load_address>
         <run_address>0x3780</run_address>
         <size>0xfe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_line</name>
         <load_address>0x387e</load_address>
         <run_address>0x387e</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_line</name>
         <load_address>0x393e</load_address>
         <run_address>0x393e</run_address>
         <size>0x1c8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-96">
         <name>.debug_line</name>
         <load_address>0x3b06</load_address>
         <run_address>0x3b06</run_address>
         <size>0x67</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-ca">
         <name>.debug_line</name>
         <load_address>0x3b6d</load_address>
         <run_address>0x3b6d</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_line</name>
         <load_address>0x3bae</load_address>
         <run_address>0x3bae</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-7e">
         <name>.debug_line</name>
         <load_address>0x3c52</load_address>
         <run_address>0x3c52</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e1"/>
      </object_component>
      <object_component id="oc-46">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-7c">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e1"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x7d8</size>
         <contents>
            <object_component_ref idref="oc-e7"/>
            <object_component_ref idref="oc-de"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-70"/>
            <object_component_ref idref="oc-b7"/>
            <object_component_ref idref="oc-b4"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-b5"/>
            <object_component_ref idref="oc-e4"/>
            <object_component_ref idref="oc-8a"/>
            <object_component_ref idref="oc-9a"/>
            <object_component_ref idref="oc-b3"/>
            <object_component_ref idref="oc-55"/>
            <object_component_ref idref="oc-8f"/>
            <object_component_ref idref="oc-e9"/>
            <object_component_ref idref="oc-e6"/>
            <object_component_ref idref="oc-e8"/>
            <object_component_ref idref="oc-85"/>
            <object_component_ref idref="oc-4c"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-c0"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-da"/>
            <object_component_ref idref="oc-b8"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-93"/>
            <object_component_ref idref="oc-c9"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-6c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x8d0</load_address>
         <run_address>0x8d0</run_address>
         <size>0x30</size>
         <contents>
            <object_component_ref idref="oc-13d"/>
            <object_component_ref idref="oc-140"/>
            <object_component_ref idref="oc-13f"/>
            <object_component_ref idref="oc-13e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0x898</load_address>
         <run_address>0x898</run_address>
         <size>0x38</size>
         <contents>
            <object_component_ref idref="oc-e5"/>
            <object_component_ref idref="oc-eb"/>
            <object_component_ref idref="oc-ea"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-107"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x20200058</run_address>
         <size>0x8</size>
         <contents>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-61"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x20200000</run_address>
         <size>0x58</size>
         <contents>
            <object_component_ref idref="oc-5c"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-bb"/>
            <object_component_ref idref="oc-bc"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-142"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-fe" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-ff" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-100" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-101" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-102" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-103" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-105" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-121" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x284b</size>
         <contents>
            <object_component_ref idref="oc-b6"/>
            <object_component_ref idref="oc-5f"/>
            <object_component_ref idref="oc-c8"/>
            <object_component_ref idref="oc-bf"/>
            <object_component_ref idref="oc-fb"/>
            <object_component_ref idref="oc-c4"/>
            <object_component_ref idref="oc-e2"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-9b"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-99"/>
         </contents>
      </logical_group>
      <logical_group id="lg-123" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1070</size>
         <contents>
            <object_component_ref idref="oc-b9"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-5d"/>
            <object_component_ref idref="oc-c6"/>
            <object_component_ref idref="oc-bd"/>
            <object_component_ref idref="oc-f9"/>
            <object_component_ref idref="oc-ec"/>
            <object_component_ref idref="oc-fc"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-d3"/>
            <object_component_ref idref="oc-83"/>
            <object_component_ref idref="oc-65"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-cd"/>
            <object_component_ref idref="oc-ee"/>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-ac"/>
            <object_component_ref idref="oc-144"/>
         </contents>
      </logical_group>
      <logical_group id="lg-125" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x93b4</size>
         <contents>
            <object_component_ref idref="oc-86"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-8b"/>
            <object_component_ref idref="oc-dd"/>
            <object_component_ref idref="oc-c5"/>
            <object_component_ref idref="oc-e3"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-9f"/>
            <object_component_ref idref="oc-6d"/>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-97"/>
            <object_component_ref idref="oc-cc"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-7d"/>
            <object_component_ref idref="oc-143"/>
         </contents>
      </logical_group>
      <logical_group id="lg-127" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x578</size>
         <contents>
            <object_component_ref idref="oc-88"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-8d"/>
            <object_component_ref idref="oc-c2"/>
            <object_component_ref idref="oc-e0"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-9d"/>
            <object_component_ref idref="oc-50"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-94"/>
            <object_component_ref idref="oc-7f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-129" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x6b8d</size>
         <contents>
            <object_component_ref idref="oc-ba"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-5e"/>
            <object_component_ref idref="oc-c7"/>
            <object_component_ref idref="oc-be"/>
            <object_component_ref idref="oc-fa"/>
            <object_component_ref idref="oc-ed"/>
            <object_component_ref idref="oc-fd"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-d4"/>
            <object_component_ref idref="oc-84"/>
            <object_component_ref idref="oc-66"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-ce"/>
            <object_component_ref idref="oc-ef"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12b" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x9a8</size>
         <contents>
            <object_component_ref idref="oc-87"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-91"/>
            <object_component_ref idref="oc-8e"/>
            <object_component_ref idref="oc-db"/>
            <object_component_ref idref="oc-c1"/>
            <object_component_ref idref="oc-df"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-9c"/>
            <object_component_ref idref="oc-6f"/>
            <object_component_ref idref="oc-51"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-95"/>
            <object_component_ref idref="oc-cb"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12d" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x3cf2</size>
         <contents>
            <object_component_ref idref="oc-89"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-92"/>
            <object_component_ref idref="oc-8c"/>
            <object_component_ref idref="oc-dc"/>
            <object_component_ref idref="oc-c3"/>
            <object_component_ref idref="oc-e1"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-9e"/>
            <object_component_ref idref="oc-6e"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-96"/>
            <object_component_ref idref="oc-ca"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-7e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-137" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x48</size>
         <contents>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-7c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-141" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-149" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x900</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-14a" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0x60</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-f"/>
            <logical_group_ref idref="lg-e"/>
         </contents>
      </load_segment>
      <load_segment id="lg-14b" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x20000</length>
         <used_space>0x900</used_space>
         <unused_space>0x1f700</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0x7d8</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x898</start_address>
               <size>0x38</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x8d0</start_address>
               <size>0x30</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0x900</start_address>
               <size>0x1f700</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x8000</length>
         <used_space>0x260</used_space>
         <unused_space>0x7da0</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-103"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-105"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x58</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200058</start_address>
               <size>0x8</size>
               <logical_group_ref idref="lg-e"/>
            </allocated_space>
            <available_space>
               <start_address>0x20200060</start_address>
               <size>0x7da0</size>
            </available_space>
            <allocated_space>
               <start_address>0x20207e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0xff</length>
         <used_space>0x0</used_space>
         <unused_space>0xff</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.bss</name>
            <load_address>0x8dc</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0x58</run_size>
            <compression>zero_init</compression>
         </cprec>
         <cprec>
            <name>.data</name>
            <load_address>0x8e4</load_address>
            <load_size>0x7</load_size>
            <run_address>0x20200058</run_address>
            <run_size>0x8</run_size>
            <compression>lzss</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_decompress_lzss</name>
      </handler>
      <handler>
         <index>0x1</index>
         <name>__TI_decompress_none</name>
      </handler>
      <handler>
         <index>0x2</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0x8ec</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0x8fc</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0x8fc</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0x8d0</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0x8dc</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20208000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-50">
         <name>SYSCFG_DL_init</name>
         <value>0x811</value>
         <object_component_ref idref="oc-85"/>
      </symbol>
      <symbol id="sm-51">
         <name>SYSCFG_DL_initPower</name>
         <value>0x745</value>
         <object_component_ref idref="oc-b3"/>
      </symbol>
      <symbol id="sm-52">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0x51d</value>
         <object_component_ref idref="oc-b4"/>
      </symbol>
      <symbol id="sm-53">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0x615</value>
         <object_component_ref idref="oc-b5"/>
      </symbol>
      <symbol id="sm-54">
         <name>SYSCFG_DL_Motor_init</name>
         <value>0x491</value>
         <object_component_ref idref="oc-b7"/>
      </symbol>
      <symbol id="sm-55">
         <name>SYSCFG_DL_SYSTICK_init</name>
         <value>0x87b</value>
         <object_component_ref idref="oc-b8"/>
      </symbol>
      <symbol id="sm-60">
         <name>Default_Handler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-61">
         <name>Reset_Handler</name>
         <value>0x88f</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-62">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-63">
         <name>NMI_Handler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-64">
         <name>HardFault_Handler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-65">
         <name>SVC_Handler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-66">
         <name>PendSV_Handler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-67">
         <name>GROUP0_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-68">
         <name>TIMG8_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-69">
         <name>UART3_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6a">
         <name>ADC0_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6b">
         <name>ADC1_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6c">
         <name>CANFD0_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6d">
         <name>DAC0_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6e">
         <name>SPI0_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6f">
         <name>SPI1_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-70">
         <name>UART1_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-71">
         <name>UART2_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-72">
         <name>UART0_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-73">
         <name>TIMG0_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-74">
         <name>TIMG6_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-75">
         <name>TIMA0_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-76">
         <name>TIMA1_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-77">
         <name>TIMG7_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-78">
         <name>TIMG12_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-79">
         <name>I2C0_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7a">
         <name>I2C1_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7b">
         <name>AES_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7c">
         <name>RTC_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7d">
         <name>DMA_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8e">
         <name>main</name>
         <value>0x3f9</value>
         <object_component_ref idref="oc-70"/>
      </symbol>
      <symbol id="sm-8f">
         <name>tick_ms</name>
         <value>0x20200054</value>
      </symbol>
      <symbol id="sm-90">
         <name>gEncoderCount_R</name>
         <value>0x2020005c</value>
         <object_component_ref idref="oc-62"/>
      </symbol>
      <symbol id="sm-91">
         <name>gEncoderCount_L</name>
         <value>0x20200058</value>
         <object_component_ref idref="oc-61"/>
      </symbol>
      <symbol id="sm-92">
         <name>GROUP1_IRQHandler</name>
         <value>0x2a1</value>
         <object_component_ref idref="oc-39"/>
      </symbol>
      <symbol id="sm-93">
         <name>gpioB</name>
         <value>0x20200050</value>
      </symbol>
      <symbol id="sm-94">
         <name>SysTick_Handler</name>
         <value>0x861</value>
         <object_component_ref idref="oc-34"/>
      </symbol>
      <symbol id="sm-9e">
         <name>Set_PWM_Duty</name>
         <value>0x7a1</value>
         <object_component_ref idref="oc-8f"/>
      </symbol>
      <symbol id="sm-aa">
         <name>PID_App_Init</name>
         <value>0x6c5</value>
         <object_component_ref idref="oc-8a"/>
      </symbol>
      <symbol id="sm-ab">
         <name>pid_left</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-ac">
         <name>pid_right</name>
         <value>0x20200028</value>
      </symbol>
      <symbol id="sm-ad">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-ae">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-af">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-b0">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-b1">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-b2">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-b3">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-b4">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-b5">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-be">
         <name>DL_Common_delayCycles</name>
         <value>0x871</value>
         <object_component_ref idref="oc-da"/>
      </symbol>
      <symbol id="sm-d5">
         <name>DL_Timer_setClockConfig</name>
         <value>0x7dd</value>
         <object_component_ref idref="oc-e6"/>
      </symbol>
      <symbol id="sm-d6">
         <name>DL_Timer_setCaptureCompareValue</name>
         <value>0x851</value>
         <object_component_ref idref="oc-c0"/>
      </symbol>
      <symbol id="sm-d7">
         <name>DL_Timer_setCaptCompUpdateMethod</name>
         <value>0x7c1</value>
         <object_component_ref idref="oc-e9"/>
      </symbol>
      <symbol id="sm-d8">
         <name>DL_Timer_setCaptureCompareOutCtl</name>
         <value>0x7f9</value>
         <object_component_ref idref="oc-e8"/>
      </symbol>
      <symbol id="sm-d9">
         <name>DL_Timer_initFourCCPWMMode</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-e7"/>
      </symbol>
      <symbol id="sm-e7">
         <name>DL_SYSCTL_configSYSPLL</name>
         <value>0x1c5</value>
         <object_component_ref idref="oc-de"/>
      </symbol>
      <symbol id="sm-e8">
         <name>DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK</name>
         <value>0x681</value>
         <object_component_ref idref="oc-e4"/>
      </symbol>
      <symbol id="sm-f3">
         <name>_c_int00_noargs</name>
         <value>0x779</value>
         <object_component_ref idref="oc-55"/>
      </symbol>
      <symbol id="sm-f4">
         <name>__stack</name>
         <value>0x20207e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-100">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0x709</value>
         <object_component_ref idref="oc-9a"/>
      </symbol>
      <symbol id="sm-108">
         <name>_system_pre_init</name>
         <value>0x893</value>
         <object_component_ref idref="oc-6c"/>
      </symbol>
      <symbol id="sm-113">
         <name>__TI_zero_init_nomemset</name>
         <value>0x829</value>
         <object_component_ref idref="oc-4c"/>
      </symbol>
      <symbol id="sm-11c">
         <name>__TI_decompress_none</name>
         <value>0x83f</value>
         <object_component_ref idref="oc-20"/>
      </symbol>
      <symbol id="sm-127">
         <name>__TI_decompress_lzss</name>
         <value>0x599</value>
         <object_component_ref idref="oc-1c"/>
      </symbol>
      <symbol id="sm-131">
         <name>abort</name>
         <value>0x885</value>
         <object_component_ref idref="oc-93"/>
      </symbol>
      <symbol id="sm-13b">
         <name>HOSTexit</name>
         <value>0x88b</value>
         <object_component_ref idref="oc-c9"/>
      </symbol>
      <symbol id="sm-13c">
         <name>C$$EXIT</name>
         <value>0x88a</value>
         <object_component_ref idref="oc-c9"/>
      </symbol>
      <symbol id="sm-142">
         <name>__aeabi_memcpy</name>
         <value>0x87d</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-143">
         <name>__aeabi_memcpy4</name>
         <value>0x87d</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-144">
         <name>__aeabi_memcpy8</name>
         <value>0x87d</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-15d">
         <name>memcpy</name>
         <value>0x35d</value>
         <object_component_ref idref="oc-80"/>
      </symbol>
      <symbol id="sm-15e">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-161">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-162">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
