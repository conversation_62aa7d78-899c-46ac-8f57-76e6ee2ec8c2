<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v4.0.3.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <command_line>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\bin\tiarmlnk -IC:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib -o PTZ_Car.out -mPTZ_Car.map -iC:/ti/mspm0_sdk_2_04_00_06/source -iC:/Users/<USER>/workspace_ccstheia/PTZ_Car -iC:/Users/<USER>/workspace_ccstheia/PTZ_Car/Debug/syscfg -iC:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib --diag_wrap=off --display_error_number --warn_sections --xml_link_info=PTZ_Car_linkInfo.xml --rom_model ./ti_msp_dl_config.o ./startup_mspm0g350x_ticlang.o ./main.o ./Hardware/Motor.o ./Hardware/pid.o ./Hardware/pid_app.o -l./device_linker.cmd -ldevice.cmd.genlibs -llibc.a --start-group -llibc++.a -llibc++abi.a -llibc.a -llibsys.a -llibsysbm.a -llibclang_rt.builtins.a -llibclang_rt.profile.a --end-group --cg_opt_level=2</command_line>
   <link_time>0x6889cf30</link_time>
   <link_errors>0x0</link_errors>
   <output_file>C:\Users\<USER>\workspace_ccstheia\PTZ_Car\Debug\PTZ_Car.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0x771</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>C:\Users\<USER>\workspace_ccstheia\PTZ_Car\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>C:\Users\<USER>\workspace_ccstheia\PTZ_Car\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>C:\Users\<USER>\workspace_ccstheia\PTZ_Car\Debug\.\</path>
         <kind>object</kind>
         <file>main.o</file>
         <name>main.o</name>
      </input_file>
      <input_file id="fl-4">
         <path>C:\Users\<USER>\workspace_ccstheia\PTZ_Car\Debug\.\Hardware\</path>
         <kind>object</kind>
         <file>Motor.o</file>
         <name>Motor.o</name>
      </input_file>
      <input_file id="fl-5">
         <path>C:\Users\<USER>\workspace_ccstheia\PTZ_Car\Debug\.\Hardware\</path>
         <kind>object</kind>
         <file>pid.o</file>
         <name>pid.o</name>
      </input_file>
      <input_file id="fl-6">
         <path>C:\Users\<USER>\workspace_ccstheia\PTZ_Car\Debug\.\Hardware\</path>
         <kind>object</kind>
         <file>pid_app.o</file>
         <name>pid_app.o</name>
      </input_file>
      <input_file id="fl-13">
         <path>C:\Users\<USER>\workspace_ccstheia\PTZ_Car\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-14">
         <path>C:\ti\mspm0_sdk_2_04_00_06\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-15">
         <path>C:\ti\mspm0_sdk_2_04_00_06\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_timer.o</name>
      </input_file>
      <input_file id="fl-16">
         <path>C:\ti\mspm0_sdk_2_04_00_06\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_sysctl_mspm0g1x0x_g3x0x.o</name>
      </input_file>
      <input_file id="fl-2d">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-2e">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-2f">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-30">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-31">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-32">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-33">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-34">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-35">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-36">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-37">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-d7">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostexit.c.obj</name>
      </input_file>
      <input_file id="fl-d8">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>addsf3.S.obj</name>
      </input_file>
      <input_file id="fl-d9">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>mulsf3.S.obj</name>
      </input_file>
      <input_file id="fl-da">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixsfsi.S.obj</name>
      </input_file>
      <input_file id="fl-db">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatsisf.S.obj</name>
      </input_file>
      <input_file id="fl-dc">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_fcmp.S.obj</name>
      </input_file>
      <input_file id="fl-dd">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-de">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-df">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>comparesf2.S.obj</name>
      </input_file>
      <input_file id="fl-e0">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldsi3.S.obj</name>
      </input_file>
      <input_file id="fl-e1">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-e2">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-e6">
         <name>.text.DL_Timer_initFourCCPWMMode</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0x104</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-dd">
         <name>.text.DL_SYSCTL_configSYSPLL</name>
         <load_address>0x1c4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c4</run_address>
         <size>0xdc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-39">
         <name>.text.GROUP1_IRQHandler</name>
         <load_address>0x2a0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2a0</run_address>
         <size>0xbc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-7f">
         <name>.text:memcpy</name>
         <load_address>0x35c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x35c</run_address>
         <size>0x9a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e1"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x3f6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x3f6</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-6f">
         <name>.text.main</name>
         <load_address>0x3f8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x3f8</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-b6">
         <name>.text.SYSCFG_DL_Motor_init</name>
         <load_address>0x488</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x488</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b3">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0x514</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x514</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.text:decompress:lzss</name>
         <load_address>0x590</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x590</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-b4">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0x60c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x60c</run_address>
         <size>0x6c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-e3">
         <name>.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK</name>
         <load_address>0x678</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x678</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-89">
         <name>.text.PID_App_Init</name>
         <load_address>0x6bc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6bc</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-99">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0x700</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x700</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-b2">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0x73c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x73c</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-55">
         <name>.text:_c_int00_noargs</name>
         <load_address>0x770</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x770</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-8e">
         <name>.text.Set_PWM_Duty</name>
         <load_address>0x798</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x798</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-e8">
         <name>.text.DL_Timer_setCaptCompUpdateMethod</name>
         <load_address>0x7b8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7b8</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-e5">
         <name>.text.DL_Timer_setClockConfig</name>
         <load_address>0x7d4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7d4</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-e7">
         <name>.text.DL_Timer_setCaptureCompareOutCtl</name>
         <load_address>0x7f0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7f0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-84">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0x808</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x808</run_address>
         <size>0x18</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-4c">
         <name>.text:decompress:ZI:__TI_zero_init_nomemset</name>
         <load_address>0x820</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x820</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-20">
         <name>.text:decompress:none</name>
         <load_address>0x836</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x836</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-bf">
         <name>.text.DL_Timer_setCaptureCompareValue</name>
         <load_address>0x848</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x848</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-34">
         <name>.text.SysTick_Handler</name>
         <load_address>0x858</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x858</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-d9">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0x868</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x868</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-b7">
         <name>.text.SYSCFG_DL_SYSTICK_init</name>
         <load_address>0x872</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x872</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-45">
         <name>.text.__aeabi_memcpy</name>
         <load_address>0x874</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x874</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-92">
         <name>.text:abort</name>
         <load_address>0x87c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x87c</run_address>
         <size>0x6</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-c8">
         <name>.text.HOSTexit</name>
         <load_address>0x882</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x882</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0x886</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x886</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-6b">
         <name>.text._system_pre_init</name>
         <load_address>0x88a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x88a</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-13c">
         <name>__TI_handler_table</name>
         <load_address>0x8c8</load_address>
         <readonly>true</readonly>
         <run_address>0x8c8</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-13f">
         <name>.cinit..bss.load</name>
         <load_address>0x8d4</load_address>
         <readonly>true</readonly>
         <run_address>0x8d4</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-13e">
         <name>.cinit..data.load</name>
         <load_address>0x8dc</load_address>
         <readonly>true</readonly>
         <run_address>0x8dc</run_address>
         <size>0x7</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-13d">
         <name>__TI_cinit_table</name>
         <load_address>0x8e4</load_address>
         <readonly>true</readonly>
         <run_address>0x8e4</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-e4">
         <name>.rodata.gSYSPLLConfig</name>
         <load_address>0x890</load_address>
         <readonly>true</readonly>
         <run_address>0x890</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-ea">
         <name>.rodata.gMotorConfig</name>
         <load_address>0x8b8</load_address>
         <readonly>true</readonly>
         <run_address>0x8b8</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-e9">
         <name>.rodata.gMotorClockConfig</name>
         <load_address>0x8c0</load_address>
         <readonly>true</readonly>
         <run_address>0x8c0</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-106">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-61">
         <name>.data.gEncoderCount_R</name>
         <load_address>0x2020005c</load_address>
         <readwrite>true</readwrite>
         <run_address>0x2020005c</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-60">
         <name>.data.gEncoderCount_L</name>
         <load_address>0x20200058</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200058</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-5c">
         <name>.common:tick_ms</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200054</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-5f">
         <name>.common:gpioB</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200050</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-ba">
         <name>.common:pid_left</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-bb">
         <name>.common:pid_right</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200028</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-141">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-b5">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x82</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-c7">
         <name>.debug_loc</name>
         <load_address>0x82</load_address>
         <run_address>0x82</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-be">
         <name>.debug_loc</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x68</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-fa">
         <name>.debug_loc</name>
         <load_address>0x128</load_address>
         <run_address>0x128</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-c2">
         <name>.debug_loc</name>
         <load_address>0x13b</load_address>
         <run_address>0x13b</run_address>
         <size>0x1a27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-de">
         <name>.debug_loc</name>
         <load_address>0x1b62</load_address>
         <run_address>0x1b62</run_address>
         <size>0x414</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_loc</name>
         <load_address>0x1f76</load_address>
         <run_address>0x1f76</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-9d">
         <name>.debug_loc</name>
         <load_address>0x204e</load_address>
         <run_address>0x204e</run_address>
         <size>0x424</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-51">
         <name>.debug_loc</name>
         <load_address>0x2472</load_address>
         <run_address>0x2472</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.debug_loc</name>
         <load_address>0x25de</load_address>
         <run_address>0x25de</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-43">
         <name>.debug_loc</name>
         <load_address>0x264d</load_address>
         <run_address>0x264d</run_address>
         <size>0x167</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-98">
         <name>.debug_loc</name>
         <load_address>0x27b4</load_address>
         <run_address>0x27b4</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-b8">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1f5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_abbrev</name>
         <load_address>0x1f5</load_address>
         <run_address>0x1f5</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-5d">
         <name>.debug_abbrev</name>
         <load_address>0x262</load_address>
         <run_address>0x262</run_address>
         <size>0x1cc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-c5">
         <name>.debug_abbrev</name>
         <load_address>0x42e</load_address>
         <run_address>0x42e</run_address>
         <size>0xef</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-bc">
         <name>.debug_abbrev</name>
         <load_address>0x51d</load_address>
         <run_address>0x51d</run_address>
         <size>0x105</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-f8">
         <name>.debug_abbrev</name>
         <load_address>0x622</load_address>
         <run_address>0x622</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-eb">
         <name>.debug_abbrev</name>
         <load_address>0x684</load_address>
         <run_address>0x684</run_address>
         <size>0x286</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-fb">
         <name>.debug_abbrev</name>
         <load_address>0x90a</load_address>
         <run_address>0x90a</run_address>
         <size>0x218</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_abbrev</name>
         <load_address>0xb22</load_address>
         <run_address>0xb22</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-d2">
         <name>.debug_abbrev</name>
         <load_address>0xbd1</load_address>
         <run_address>0xbd1</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-82">
         <name>.debug_abbrev</name>
         <load_address>0xd41</load_address>
         <run_address>0xd41</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-64">
         <name>.debug_abbrev</name>
         <load_address>0xd7a</load_address>
         <run_address>0xd7a</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_abbrev</name>
         <load_address>0xe3c</load_address>
         <run_address>0xe3c</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_abbrev</name>
         <load_address>0xeac</load_address>
         <run_address>0xeac</run_address>
         <size>0x8d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-cc">
         <name>.debug_abbrev</name>
         <load_address>0xf39</load_address>
         <run_address>0xf39</run_address>
         <size>0x98</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-ed">
         <name>.debug_abbrev</name>
         <load_address>0xfd1</load_address>
         <run_address>0xfd1</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-63">
         <name>.debug_abbrev</name>
         <load_address>0xffd</load_address>
         <run_address>0xffd</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-ab">
         <name>.debug_abbrev</name>
         <load_address>0x1024</load_address>
         <run_address>0x1024</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e1"/>
      </object_component>
      <object_component id="oc-143">
         <name>.debug_abbrev</name>
         <load_address>0x1049</load_address>
         <run_address>0x1049</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-88">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x238b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x238b</load_address>
         <run_address>0x238b</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_info</name>
         <load_address>0x240b</load_address>
         <run_address>0x240b</run_address>
         <size>0xd56</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-90">
         <name>.debug_info</name>
         <load_address>0x3161</load_address>
         <run_address>0x3161</run_address>
         <size>0x737</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-8a">
         <name>.debug_info</name>
         <load_address>0x3898</load_address>
         <run_address>0x3898</run_address>
         <size>0x29c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-db">
         <name>.debug_info</name>
         <load_address>0x3b34</load_address>
         <run_address>0x3b34</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-c4">
         <name>.debug_info</name>
         <load_address>0x3ba9</load_address>
         <run_address>0x3ba9</run_address>
         <size>0x3172</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-df">
         <name>.debug_info</name>
         <load_address>0x6d1b</load_address>
         <run_address>0x6d1b</run_address>
         <size>0x1090</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0x7dab</load_address>
         <run_address>0x7dab</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-9b">
         <name>.debug_info</name>
         <load_address>0x81ce</load_address>
         <run_address>0x81ce</run_address>
         <size>0x744</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-6d">
         <name>.debug_info</name>
         <load_address>0x8912</load_address>
         <run_address>0x8912</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_info</name>
         <load_address>0x8958</load_address>
         <run_address>0x8958</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_info</name>
         <load_address>0x8aea</load_address>
         <run_address>0x8aea</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_info</name>
         <load_address>0x8bb0</load_address>
         <run_address>0x8bb0</run_address>
         <size>0x17c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-95">
         <name>.debug_info</name>
         <load_address>0x8d2c</load_address>
         <run_address>0x8d2c</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-ca">
         <name>.debug_info</name>
         <load_address>0x8e24</load_address>
         <run_address>0x8e24</run_address>
         <size>0x3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_info</name>
         <load_address>0x8e5f</load_address>
         <run_address>0x8e5f</run_address>
         <size>0x199</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-7d">
         <name>.debug_info</name>
         <load_address>0x8ff8</load_address>
         <run_address>0x8ff8</run_address>
         <size>0x2fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e1"/>
      </object_component>
      <object_component id="oc-142">
         <name>.debug_info</name>
         <load_address>0x92f2</load_address>
         <run_address>0x92f2</run_address>
         <size>0xa3</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-85">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x38</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_ranges</name>
         <load_address>0x38</load_address>
         <run_address>0x38</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_ranges</name>
         <load_address>0x50</load_address>
         <run_address>0x50</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-8c">
         <name>.debug_ranges</name>
         <load_address>0x78</load_address>
         <run_address>0x78</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-c0">
         <name>.debug_ranges</name>
         <load_address>0xa0</load_address>
         <run_address>0xa0</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-e0">
         <name>.debug_ranges</name>
         <load_address>0x278</load_address>
         <run_address>0x278</run_address>
         <size>0x1a8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.debug_ranges</name>
         <load_address>0x420</load_address>
         <run_address>0x420</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-9c">
         <name>.debug_ranges</name>
         <load_address>0x468</load_address>
         <run_address>0x468</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-50">
         <name>.debug_ranges</name>
         <load_address>0x4b0</load_address>
         <run_address>0x4b0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-44">
         <name>.debug_ranges</name>
         <load_address>0x4c8</load_address>
         <run_address>0x4c8</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-94">
         <name>.debug_ranges</name>
         <load_address>0x518</load_address>
         <run_address>0x518</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-7e">
         <name>.debug_ranges</name>
         <load_address>0x530</load_address>
         <run_address>0x530</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e1"/>
      </object_component>
      <object_component id="oc-b9">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1e0e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_str</name>
         <load_address>0x1e0e</load_address>
         <run_address>0x1e0e</run_address>
         <size>0x15a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-5e">
         <name>.debug_str</name>
         <load_address>0x1f68</load_address>
         <run_address>0x1f68</run_address>
         <size>0x839</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-c6">
         <name>.debug_str</name>
         <load_address>0x27a1</load_address>
         <run_address>0x27a1</run_address>
         <size>0x493</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-bd">
         <name>.debug_str</name>
         <load_address>0x2c34</load_address>
         <run_address>0x2c34</run_address>
         <size>0x25a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-f9">
         <name>.debug_str</name>
         <load_address>0x2e8e</load_address>
         <run_address>0x2e8e</run_address>
         <size>0x16d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-ec">
         <name>.debug_str</name>
         <load_address>0x2ffb</load_address>
         <run_address>0x2ffb</run_address>
         <size>0x1dcb</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-fc">
         <name>.debug_str</name>
         <load_address>0x4dc6</load_address>
         <run_address>0x4dc6</run_address>
         <size>0x1075</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_str</name>
         <load_address>0x5e3b</load_address>
         <run_address>0x5e3b</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-d3">
         <name>.debug_str</name>
         <load_address>0x6060</load_address>
         <run_address>0x6060</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-83">
         <name>.debug_str</name>
         <load_address>0x638f</load_address>
         <run_address>0x638f</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-65">
         <name>.debug_str</name>
         <load_address>0x6484</load_address>
         <run_address>0x6484</run_address>
         <size>0x19b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_str</name>
         <load_address>0x661f</load_address>
         <run_address>0x661f</run_address>
         <size>0x168</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_str</name>
         <load_address>0x6787</load_address>
         <run_address>0x6787</run_address>
         <size>0x1d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.debug_str</name>
         <load_address>0x695c</load_address>
         <run_address>0x695c</run_address>
         <size>0x148</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-ee">
         <name>.debug_str</name>
         <load_address>0x6aa4</load_address>
         <run_address>0x6aa4</run_address>
         <size>0xe9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-87">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x9c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_frame</name>
         <load_address>0x9c</load_address>
         <run_address>0x9c</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_frame</name>
         <load_address>0xcc</load_address>
         <run_address>0xcc</run_address>
         <size>0x68</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.debug_frame</name>
         <load_address>0x134</load_address>
         <run_address>0x134</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-8d">
         <name>.debug_frame</name>
         <load_address>0x15c</load_address>
         <run_address>0x15c</run_address>
         <size>0x68</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-da">
         <name>.debug_frame</name>
         <load_address>0x1c4</load_address>
         <run_address>0x1c4</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-c3">
         <name>.debug_frame</name>
         <load_address>0x1e4</load_address>
         <run_address>0x1e4</run_address>
         <size>0x408</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-e1">
         <name>.debug_frame</name>
         <load_address>0x5ec</load_address>
         <run_address>0x5ec</run_address>
         <size>0x12c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-54">
         <name>.debug_frame</name>
         <load_address>0x718</load_address>
         <run_address>0x718</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-9e">
         <name>.debug_frame</name>
         <load_address>0x7a8</load_address>
         <run_address>0x7a8</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-6e">
         <name>.debug_frame</name>
         <load_address>0x8a8</load_address>
         <run_address>0x8a8</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-4d">
         <name>.debug_frame</name>
         <load_address>0x8c8</load_address>
         <run_address>0x8c8</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_frame</name>
         <load_address>0x900</load_address>
         <run_address>0x900</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_frame</name>
         <load_address>0x928</load_address>
         <run_address>0x928</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-93">
         <name>.debug_frame</name>
         <load_address>0x958</load_address>
         <run_address>0x958</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-cb">
         <name>.debug_frame</name>
         <load_address>0x988</load_address>
         <run_address>0x988</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-86">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x50a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_line</name>
         <load_address>0x50a</load_address>
         <run_address>0x50a</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_line</name>
         <load_address>0x5c2</load_address>
         <run_address>0x5c2</run_address>
         <size>0x405</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-91">
         <name>.debug_line</name>
         <load_address>0x9c7</load_address>
         <run_address>0x9c7</run_address>
         <size>0x1e0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-8b">
         <name>.debug_line</name>
         <load_address>0xba7</load_address>
         <run_address>0xba7</run_address>
         <size>0x215</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-dc">
         <name>.debug_line</name>
         <load_address>0xdbc</load_address>
         <run_address>0xdbc</run_address>
         <size>0x178</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-c1">
         <name>.debug_line</name>
         <load_address>0xf34</load_address>
         <run_address>0xf34</run_address>
         <size>0x176e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-e2">
         <name>.debug_line</name>
         <load_address>0x26a2</load_address>
         <run_address>0x26a2</run_address>
         <size>0x982</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.debug_line</name>
         <load_address>0x3024</load_address>
         <run_address>0x3024</run_address>
         <size>0x1dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-9a">
         <name>.debug_line</name>
         <load_address>0x3200</load_address>
         <run_address>0x3200</run_address>
         <size>0x51a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-6c">
         <name>.debug_line</name>
         <load_address>0x371a</load_address>
         <run_address>0x371a</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.debug_line</name>
         <load_address>0x3758</load_address>
         <run_address>0x3758</run_address>
         <size>0xfe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_line</name>
         <load_address>0x3856</load_address>
         <run_address>0x3856</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_line</name>
         <load_address>0x3916</load_address>
         <run_address>0x3916</run_address>
         <size>0x1c8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-96">
         <name>.debug_line</name>
         <load_address>0x3ade</load_address>
         <run_address>0x3ade</run_address>
         <size>0x67</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-c9">
         <name>.debug_line</name>
         <load_address>0x3b45</load_address>
         <run_address>0x3b45</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_line</name>
         <load_address>0x3b86</load_address>
         <run_address>0x3b86</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-7c">
         <name>.debug_line</name>
         <load_address>0x3c2a</load_address>
         <run_address>0x3c2a</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e1"/>
      </object_component>
      <object_component id="oc-46">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-7b">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e1"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x7d0</size>
         <contents>
            <object_component_ref idref="oc-e6"/>
            <object_component_ref idref="oc-dd"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-7f"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-6f"/>
            <object_component_ref idref="oc-b6"/>
            <object_component_ref idref="oc-b3"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-b4"/>
            <object_component_ref idref="oc-e3"/>
            <object_component_ref idref="oc-89"/>
            <object_component_ref idref="oc-99"/>
            <object_component_ref idref="oc-b2"/>
            <object_component_ref idref="oc-55"/>
            <object_component_ref idref="oc-8e"/>
            <object_component_ref idref="oc-e8"/>
            <object_component_ref idref="oc-e5"/>
            <object_component_ref idref="oc-e7"/>
            <object_component_ref idref="oc-84"/>
            <object_component_ref idref="oc-4c"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-bf"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-d9"/>
            <object_component_ref idref="oc-b7"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-92"/>
            <object_component_ref idref="oc-c8"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-6b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x8c8</load_address>
         <run_address>0x8c8</run_address>
         <size>0x30</size>
         <contents>
            <object_component_ref idref="oc-13c"/>
            <object_component_ref idref="oc-13f"/>
            <object_component_ref idref="oc-13e"/>
            <object_component_ref idref="oc-13d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0x890</load_address>
         <run_address>0x890</run_address>
         <size>0x38</size>
         <contents>
            <object_component_ref idref="oc-e4"/>
            <object_component_ref idref="oc-ea"/>
            <object_component_ref idref="oc-e9"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-106"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x20200058</run_address>
         <size>0x8</size>
         <contents>
            <object_component_ref idref="oc-61"/>
            <object_component_ref idref="oc-60"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x20200000</run_address>
         <size>0x58</size>
         <contents>
            <object_component_ref idref="oc-5c"/>
            <object_component_ref idref="oc-5f"/>
            <object_component_ref idref="oc-ba"/>
            <object_component_ref idref="oc-bb"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-141"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-fd" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-fe" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-ff" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-100" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-101" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-102" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-104" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-120" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x27da</size>
         <contents>
            <object_component_ref idref="oc-b5"/>
            <object_component_ref idref="oc-c7"/>
            <object_component_ref idref="oc-be"/>
            <object_component_ref idref="oc-fa"/>
            <object_component_ref idref="oc-c2"/>
            <object_component_ref idref="oc-de"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-9d"/>
            <object_component_ref idref="oc-51"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-98"/>
         </contents>
      </logical_group>
      <logical_group id="lg-122" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1058</size>
         <contents>
            <object_component_ref idref="oc-b8"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-5d"/>
            <object_component_ref idref="oc-c5"/>
            <object_component_ref idref="oc-bc"/>
            <object_component_ref idref="oc-f8"/>
            <object_component_ref idref="oc-eb"/>
            <object_component_ref idref="oc-fb"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-d2"/>
            <object_component_ref idref="oc-82"/>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-cc"/>
            <object_component_ref idref="oc-ed"/>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-ab"/>
            <object_component_ref idref="oc-143"/>
         </contents>
      </logical_group>
      <logical_group id="lg-124" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x9395</size>
         <contents>
            <object_component_ref idref="oc-88"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-8a"/>
            <object_component_ref idref="oc-db"/>
            <object_component_ref idref="oc-c4"/>
            <object_component_ref idref="oc-df"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-9b"/>
            <object_component_ref idref="oc-6d"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-95"/>
            <object_component_ref idref="oc-ca"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-7d"/>
            <object_component_ref idref="oc-142"/>
         </contents>
      </logical_group>
      <logical_group id="lg-126" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x558</size>
         <contents>
            <object_component_ref idref="oc-85"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-8c"/>
            <object_component_ref idref="oc-c0"/>
            <object_component_ref idref="oc-e0"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-9c"/>
            <object_component_ref idref="oc-50"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-94"/>
            <object_component_ref idref="oc-7e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-128" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x6b8d</size>
         <contents>
            <object_component_ref idref="oc-b9"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-5e"/>
            <object_component_ref idref="oc-c6"/>
            <object_component_ref idref="oc-bd"/>
            <object_component_ref idref="oc-f9"/>
            <object_component_ref idref="oc-ec"/>
            <object_component_ref idref="oc-fc"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-d3"/>
            <object_component_ref idref="oc-83"/>
            <object_component_ref idref="oc-65"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-cd"/>
            <object_component_ref idref="oc-ee"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12a" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x9a8</size>
         <contents>
            <object_component_ref idref="oc-87"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-8f"/>
            <object_component_ref idref="oc-8d"/>
            <object_component_ref idref="oc-da"/>
            <object_component_ref idref="oc-c3"/>
            <object_component_ref idref="oc-e1"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-9e"/>
            <object_component_ref idref="oc-6e"/>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-93"/>
            <object_component_ref idref="oc-cb"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12c" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x3cca</size>
         <contents>
            <object_component_ref idref="oc-86"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-91"/>
            <object_component_ref idref="oc-8b"/>
            <object_component_ref idref="oc-dc"/>
            <object_component_ref idref="oc-c1"/>
            <object_component_ref idref="oc-e2"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-9a"/>
            <object_component_ref idref="oc-6c"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-96"/>
            <object_component_ref idref="oc-c9"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-7c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-136" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x48</size>
         <contents>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-7b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-140" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-148" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x8f8</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-149" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0x60</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-f"/>
            <logical_group_ref idref="lg-e"/>
         </contents>
      </load_segment>
      <load_segment id="lg-14a" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x20000</length>
         <used_space>0x8f8</used_space>
         <unused_space>0x1f708</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0x7d0</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x890</start_address>
               <size>0x38</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x8c8</start_address>
               <size>0x30</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0x8f8</start_address>
               <size>0x1f708</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x8000</length>
         <used_space>0x260</used_space>
         <unused_space>0x7da0</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-102"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-104"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x58</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200058</start_address>
               <size>0x8</size>
               <logical_group_ref idref="lg-e"/>
            </allocated_space>
            <available_space>
               <start_address>0x20200060</start_address>
               <size>0x7da0</size>
            </available_space>
            <allocated_space>
               <start_address>0x20207e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0xff</length>
         <used_space>0x0</used_space>
         <unused_space>0xff</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.bss</name>
            <load_address>0x8d4</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0x58</run_size>
            <compression>zero_init</compression>
         </cprec>
         <cprec>
            <name>.data</name>
            <load_address>0x8dc</load_address>
            <load_size>0x7</load_size>
            <run_address>0x20200058</run_address>
            <run_size>0x8</run_size>
            <compression>lzss</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_decompress_lzss</name>
      </handler>
      <handler>
         <index>0x1</index>
         <name>__TI_decompress_none</name>
      </handler>
      <handler>
         <index>0x2</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0x8e4</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0x8f4</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0x8f4</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0x8c8</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0x8d4</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20208000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-50">
         <name>SYSCFG_DL_init</name>
         <value>0x809</value>
         <object_component_ref idref="oc-84"/>
      </symbol>
      <symbol id="sm-51">
         <name>SYSCFG_DL_initPower</name>
         <value>0x73d</value>
         <object_component_ref idref="oc-b2"/>
      </symbol>
      <symbol id="sm-52">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0x515</value>
         <object_component_ref idref="oc-b3"/>
      </symbol>
      <symbol id="sm-53">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0x60d</value>
         <object_component_ref idref="oc-b4"/>
      </symbol>
      <symbol id="sm-54">
         <name>SYSCFG_DL_Motor_init</name>
         <value>0x489</value>
         <object_component_ref idref="oc-b6"/>
      </symbol>
      <symbol id="sm-55">
         <name>SYSCFG_DL_SYSTICK_init</name>
         <value>0x873</value>
         <object_component_ref idref="oc-b7"/>
      </symbol>
      <symbol id="sm-60">
         <name>Default_Handler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-61">
         <name>Reset_Handler</name>
         <value>0x887</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-62">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-63">
         <name>NMI_Handler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-64">
         <name>HardFault_Handler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-65">
         <name>SVC_Handler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-66">
         <name>PendSV_Handler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-67">
         <name>GROUP0_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-68">
         <name>TIMG8_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-69">
         <name>UART3_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6a">
         <name>ADC0_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6b">
         <name>ADC1_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6c">
         <name>CANFD0_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6d">
         <name>DAC0_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6e">
         <name>SPI0_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6f">
         <name>SPI1_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-70">
         <name>UART1_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-71">
         <name>UART2_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-72">
         <name>UART0_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-73">
         <name>TIMG0_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-74">
         <name>TIMG6_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-75">
         <name>TIMA0_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-76">
         <name>TIMA1_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-77">
         <name>TIMG7_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-78">
         <name>TIMG12_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-79">
         <name>I2C0_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7a">
         <name>I2C1_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7b">
         <name>AES_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7c">
         <name>RTC_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7d">
         <name>DMA_IRQHandler</name>
         <value>0x3f7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8d">
         <name>main</name>
         <value>0x3f9</value>
         <object_component_ref idref="oc-6f"/>
      </symbol>
      <symbol id="sm-8e">
         <name>tick_ms</name>
         <value>0x20200054</value>
      </symbol>
      <symbol id="sm-8f">
         <name>gEncoderCount_R</name>
         <value>0x2020005c</value>
         <object_component_ref idref="oc-61"/>
      </symbol>
      <symbol id="sm-90">
         <name>gEncoderCount_L</name>
         <value>0x20200058</value>
         <object_component_ref idref="oc-60"/>
      </symbol>
      <symbol id="sm-91">
         <name>GROUP1_IRQHandler</name>
         <value>0x2a1</value>
         <object_component_ref idref="oc-39"/>
      </symbol>
      <symbol id="sm-92">
         <name>gpioB</name>
         <value>0x20200050</value>
      </symbol>
      <symbol id="sm-93">
         <name>SysTick_Handler</name>
         <value>0x859</value>
         <object_component_ref idref="oc-34"/>
      </symbol>
      <symbol id="sm-9d">
         <name>Set_PWM_Duty</name>
         <value>0x799</value>
         <object_component_ref idref="oc-8e"/>
      </symbol>
      <symbol id="sm-a9">
         <name>PID_App_Init</name>
         <value>0x6bd</value>
         <object_component_ref idref="oc-89"/>
      </symbol>
      <symbol id="sm-aa">
         <name>pid_left</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-ab">
         <name>pid_right</name>
         <value>0x20200028</value>
      </symbol>
      <symbol id="sm-ac">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-ad">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-ae">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-af">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-b0">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-b1">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-b2">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-b3">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-b4">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-bd">
         <name>DL_Common_delayCycles</name>
         <value>0x869</value>
         <object_component_ref idref="oc-d9"/>
      </symbol>
      <symbol id="sm-d4">
         <name>DL_Timer_setClockConfig</name>
         <value>0x7d5</value>
         <object_component_ref idref="oc-e5"/>
      </symbol>
      <symbol id="sm-d5">
         <name>DL_Timer_setCaptureCompareValue</name>
         <value>0x849</value>
         <object_component_ref idref="oc-bf"/>
      </symbol>
      <symbol id="sm-d6">
         <name>DL_Timer_setCaptCompUpdateMethod</name>
         <value>0x7b9</value>
         <object_component_ref idref="oc-e8"/>
      </symbol>
      <symbol id="sm-d7">
         <name>DL_Timer_setCaptureCompareOutCtl</name>
         <value>0x7f1</value>
         <object_component_ref idref="oc-e7"/>
      </symbol>
      <symbol id="sm-d8">
         <name>DL_Timer_initFourCCPWMMode</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-e6"/>
      </symbol>
      <symbol id="sm-e6">
         <name>DL_SYSCTL_configSYSPLL</name>
         <value>0x1c5</value>
         <object_component_ref idref="oc-dd"/>
      </symbol>
      <symbol id="sm-e7">
         <name>DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK</name>
         <value>0x679</value>
         <object_component_ref idref="oc-e3"/>
      </symbol>
      <symbol id="sm-f2">
         <name>_c_int00_noargs</name>
         <value>0x771</value>
         <object_component_ref idref="oc-55"/>
      </symbol>
      <symbol id="sm-f3">
         <name>__stack</name>
         <value>0x20207e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-ff">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0x701</value>
         <object_component_ref idref="oc-99"/>
      </symbol>
      <symbol id="sm-107">
         <name>_system_pre_init</name>
         <value>0x88b</value>
         <object_component_ref idref="oc-6b"/>
      </symbol>
      <symbol id="sm-112">
         <name>__TI_zero_init_nomemset</name>
         <value>0x821</value>
         <object_component_ref idref="oc-4c"/>
      </symbol>
      <symbol id="sm-11b">
         <name>__TI_decompress_none</name>
         <value>0x837</value>
         <object_component_ref idref="oc-20"/>
      </symbol>
      <symbol id="sm-126">
         <name>__TI_decompress_lzss</name>
         <value>0x591</value>
         <object_component_ref idref="oc-1c"/>
      </symbol>
      <symbol id="sm-130">
         <name>abort</name>
         <value>0x87d</value>
         <object_component_ref idref="oc-92"/>
      </symbol>
      <symbol id="sm-13a">
         <name>HOSTexit</name>
         <value>0x883</value>
         <object_component_ref idref="oc-c8"/>
      </symbol>
      <symbol id="sm-13b">
         <name>C$$EXIT</name>
         <value>0x882</value>
         <object_component_ref idref="oc-c8"/>
      </symbol>
      <symbol id="sm-141">
         <name>__aeabi_memcpy</name>
         <value>0x875</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-142">
         <name>__aeabi_memcpy4</name>
         <value>0x875</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-143">
         <name>__aeabi_memcpy8</name>
         <value>0x875</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-15c">
         <name>memcpy</name>
         <value>0x35d</value>
         <object_component_ref idref="oc-7f"/>
      </symbol>
      <symbol id="sm-15d">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-160">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-161">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
