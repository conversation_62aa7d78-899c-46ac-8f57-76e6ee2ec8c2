******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Wed Jul 30 15:53:56 2025

OUTPUT FILE NAME:   <PTZ_Car.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00000779


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00000900  0001f700  R  X
  SRAM                  20200000   00008000  00000260  00007da0  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00000900   00000900    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000007d8   000007d8    r-x .text
  00000898    00000898    00000038   00000038    r-- .rodata
  000008d0    000008d0    00000030   00000030    r-- .cinit
20200000    20200000    00000060   00000000    rw-
  20200000    20200000    00000058   00000000    rw- .bss
  20200058    20200058    00000008   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000007d8     
                  000000c0    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  000001c4    000000dc                 : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  000002a0    000000bc     main.o (.text.GROUP1_IRQHandler)
                  0000035c    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  000003f6    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  000003f8    00000098     main.o (.text.main)
                  00000490    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_Motor_init)
                  0000051c    0000007c     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000598    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00000614    0000006c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00000680    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  000006c4    00000044     pid_app.o (.text.PID_App_Init)
                  00000708    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00000744    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00000778    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000007a0    00000020     Motor.o (.text.Set_PWM_Duty)
                  000007c0    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  000007dc    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  000007f8    00000018                 : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00000810    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00000828    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  0000083e    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00000850    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00000860    00000010     main.o (.text.SysTick_Handler)
                  00000870    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0000087a    00000002     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  0000087c    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00000884    00000006     libc.a : exit.c.obj (.text:abort)
                  0000088a    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  0000088e    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00000892    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00000896    00000002     --HOLE-- [fill = 0]

.cinit     0    000008d0    00000030     
                  000008d0    0000000c     (__TI_handler_table)
                  000008dc    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000008e4    00000007     (.cinit..data.load) [load image, compression = lzss]
                  000008eb    00000001     --HOLE-- [fill = 0]
                  000008ec    00000010     (__TI_cinit_table)
                  000008fc    00000004     --HOLE-- [fill = 0]

.rodata    0    00000898    00000038     
                  00000898    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  000008c0    00000008     ti_msp_dl_config.o (.rodata.gMotorConfig)
                  000008c8    00000003     ti_msp_dl_config.o (.rodata.gMotorClockConfig)
                  000008cb    00000005     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000058     UNINITIALIZED
                  20200000    00000028     (.common:pid_left)
                  20200028    00000028     (.common:pid_right)
                  20200050    00000004     (.common:gpioB)
                  20200054    00000004     (.common:tick_ms)

.data      0    20200058    00000008     UNINITIALIZED
                  20200058    00000004     main.o (.data.gEncoderCount_L)
                  2020005c    00000004     main.o (.data.gEncoderCount_R)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             450    51        0      
       main.o                         356    0         16     
       startup_mspm0g350x_ticlang.o   6      192       0      
    +--+------------------------------+------+---------+---------+
       Total:                         812    243       16     
                                                              
    .\Hardware\
       pid_app.o                      68     0         80     
       Motor.o                        32     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         100    0         80     
                                                              
    C:/ti/mspm0_sdk_2_04_00_06/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     356    0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o   288    0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         654    0         0      
                                                              
    C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       memcpy16.S.obj                 154    0         0      
       copy_decompress_lzss.c.obj     124    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         428    0         0      
                                                              
    C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       aeabi_memcpy.S.obj             8      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         8      0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      43        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   2006   286       608    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000008ec records: 2, size/record: 8, table size: 16
	.bss: load addr=000008dc, load size=00000008 bytes, run addr=20200000, run size=00000058 bytes, compression=zero_init
	.data: load addr=000008e4, load size=00000007 bytes, run addr=20200058, run size=00000008 bytes, compression=lzss


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000008d0 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
000003f7  ADC0_IRQHandler                      
000003f7  ADC1_IRQHandler                      
000003f7  AES_IRQHandler                       
0000088a  C$$EXIT                              
000003f7  CANFD0_IRQHandler                    
000003f7  DAC0_IRQHandler                      
00000871  DL_Common_delayCycles                
000001c5  DL_SYSCTL_configSYSPLL               
00000681  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000000c1  DL_Timer_initFourCCPWMMode           
000007c1  DL_Timer_setCaptCompUpdateMethod     
000007f9  DL_Timer_setCaptureCompareOutCtl     
00000851  DL_Timer_setCaptureCompareValue      
000007dd  DL_Timer_setClockConfig              
000003f7  DMA_IRQHandler                       
000003f7  Default_Handler                      
000003f7  GROUP0_IRQHandler                    
000002a1  GROUP1_IRQHandler                    
0000088b  HOSTexit                             
000003f7  HardFault_Handler                    
000003f7  I2C0_IRQHandler                      
000003f7  I2C1_IRQHandler                      
000003f7  NMI_Handler                          
000006c5  PID_App_Init                         
000003f7  PendSV_Handler                       
000003f7  RTC_IRQHandler                       
0000088f  Reset_Handler                        
000003f7  SPI0_IRQHandler                      
000003f7  SPI1_IRQHandler                      
000003f7  SVC_Handler                          
0000051d  SYSCFG_DL_GPIO_init                  
00000491  SYSCFG_DL_Motor_init                 
00000615  SYSCFG_DL_SYSCTL_init                
0000087b  SYSCFG_DL_SYSTICK_init               
00000811  SYSCFG_DL_init                       
00000745  SYSCFG_DL_initPower                  
000007a1  Set_PWM_Duty                         
00000861  SysTick_Handler                      
000003f7  TIMA0_IRQHandler                     
000003f7  TIMA1_IRQHandler                     
000003f7  TIMG0_IRQHandler                     
000003f7  TIMG12_IRQHandler                    
000003f7  TIMG6_IRQHandler                     
000003f7  TIMG7_IRQHandler                     
000003f7  TIMG8_IRQHandler                     
000003f7  UART0_IRQHandler                     
000003f7  UART1_IRQHandler                     
000003f7  UART2_IRQHandler                     
000003f7  UART3_IRQHandler                     
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
000008ec  __TI_CINIT_Base                      
000008fc  __TI_CINIT_Limit                     
000008fc  __TI_CINIT_Warm                      
000008d0  __TI_Handler_Table_Base              
000008dc  __TI_Handler_Table_Limit             
00000709  __TI_auto_init_nobinit_nopinit       
00000599  __TI_decompress_lzss                 
0000083f  __TI_decompress_none                 
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00000829  __TI_zero_init_nomemset              
0000087d  __aeabi_memcpy                       
0000087d  __aeabi_memcpy4                      
0000087d  __aeabi_memcpy8                      
ffffffff  __binit__                            
UNDEFED   __mpu_init                           
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00000779  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00000893  _system_pre_init                     
00000885  abort                                
ffffffff  binit                                
20200058  gEncoderCount_L                      
2020005c  gEncoderCount_R                      
20200050  gpioB                                
00000000  interruptVectors                     
000003f9  main                                 
0000035d  memcpy                               
20200000  pid_left                             
20200028  pid_right                            
20200054  tick_ms                              


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  DL_Timer_initFourCCPWMMode           
000001c5  DL_SYSCTL_configSYSPLL               
00000200  __STACK_SIZE                         
000002a1  GROUP1_IRQHandler                    
0000035d  memcpy                               
000003f7  ADC0_IRQHandler                      
000003f7  ADC1_IRQHandler                      
000003f7  AES_IRQHandler                       
000003f7  CANFD0_IRQHandler                    
000003f7  DAC0_IRQHandler                      
000003f7  DMA_IRQHandler                       
000003f7  Default_Handler                      
000003f7  GROUP0_IRQHandler                    
000003f7  HardFault_Handler                    
000003f7  I2C0_IRQHandler                      
000003f7  I2C1_IRQHandler                      
000003f7  NMI_Handler                          
000003f7  PendSV_Handler                       
000003f7  RTC_IRQHandler                       
000003f7  SPI0_IRQHandler                      
000003f7  SPI1_IRQHandler                      
000003f7  SVC_Handler                          
000003f7  TIMA0_IRQHandler                     
000003f7  TIMA1_IRQHandler                     
000003f7  TIMG0_IRQHandler                     
000003f7  TIMG12_IRQHandler                    
000003f7  TIMG6_IRQHandler                     
000003f7  TIMG7_IRQHandler                     
000003f7  TIMG8_IRQHandler                     
000003f7  UART0_IRQHandler                     
000003f7  UART1_IRQHandler                     
000003f7  UART2_IRQHandler                     
000003f7  UART3_IRQHandler                     
000003f9  main                                 
00000491  SYSCFG_DL_Motor_init                 
0000051d  SYSCFG_DL_GPIO_init                  
00000599  __TI_decompress_lzss                 
00000615  SYSCFG_DL_SYSCTL_init                
00000681  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000006c5  PID_App_Init                         
00000709  __TI_auto_init_nobinit_nopinit       
00000745  SYSCFG_DL_initPower                  
00000779  _c_int00_noargs                      
000007a1  Set_PWM_Duty                         
000007c1  DL_Timer_setCaptCompUpdateMethod     
000007dd  DL_Timer_setClockConfig              
000007f9  DL_Timer_setCaptureCompareOutCtl     
00000811  SYSCFG_DL_init                       
00000829  __TI_zero_init_nomemset              
0000083f  __TI_decompress_none                 
00000851  DL_Timer_setCaptureCompareValue      
00000861  SysTick_Handler                      
00000871  DL_Common_delayCycles                
0000087b  SYSCFG_DL_SYSTICK_init               
0000087d  __aeabi_memcpy                       
0000087d  __aeabi_memcpy4                      
0000087d  __aeabi_memcpy8                      
00000885  abort                                
0000088a  C$$EXIT                              
0000088b  HOSTexit                             
0000088f  Reset_Handler                        
00000893  _system_pre_init                     
000008d0  __TI_Handler_Table_Base              
000008dc  __TI_Handler_Table_Limit             
000008ec  __TI_CINIT_Base                      
000008fc  __TI_CINIT_Limit                     
000008fc  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200000  pid_left                             
20200028  pid_right                            
20200050  gpioB                                
20200054  tick_ms                              
20200058  gEncoderCount_L                      
2020005c  gEncoderCount_R                      
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[97 symbols]
