******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Wed Jul 30 14:37:47 2025

OUTPUT FILE NAME:   <PTZ_Car.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00000789


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000008f0  0001f710  R  X
  SRAM                  20200000   00008000  00000210  00007df0  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000008f0   000008f0    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000007c8   000007c8    r-x .text
  00000888    00000888    00000038   00000038    r-- .rodata
  000008c0    000008c0    00000030   00000030    r-- .cinit
20200000    20200000    00000010   00000000    rw-
  20200000    20200000    00000008   00000000    rw- .bss
  20200008    20200008    00000008   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000007c8     
                  000000c0    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  000001c4    000000dc                 : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  000002a0    000000bc     main.o (.text.GROUP1_IRQHandler)
                  0000035c    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  000003f6    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  000003f8    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_Motor_init)
                  00000484    0000008c     main.o (.text.main)
                  00000510    00000080     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000590    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  0000060c    0000006c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00000678    0000005c     Motor.o (.text.Set_PWM_Duty)
                  000006d4    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00000718    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00000754    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00000788    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000007b0    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  000007cc    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  000007e8    00000018                 : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00000800    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00000818    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  0000082e    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00000840    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00000850    00000010     main.o (.text.SysTick_Handler)
                  00000860    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0000086a    00000002     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  0000086c    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00000874    00000006     libc.a : exit.c.obj (.text:abort)
                  0000087a    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  0000087e    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00000882    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00000886    00000002     --HOLE-- [fill = 0]

.cinit     0    000008c0    00000030     
                  000008c0    0000000c     (__TI_handler_table)
                  000008cc    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000008d4    00000007     (.cinit..data.load) [load image, compression = lzss]
                  000008db    00000001     --HOLE-- [fill = 0]
                  000008dc    00000010     (__TI_cinit_table)
                  000008ec    00000004     --HOLE-- [fill = 0]

.rodata    0    00000888    00000038     
                  00000888    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  000008b0    00000008     ti_msp_dl_config.o (.rodata.gMotorConfig)
                  000008b8    00000003     ti_msp_dl_config.o (.rodata.gMotorClockConfig)
                  000008bb    00000005     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000008     UNINITIALIZED
                  20200000    00000004     (.common:gpioB)
                  20200004    00000004     (.common:tick_ms)

.data      0    20200008    00000008     UNINITIALIZED
                  20200008    00000004     main.o (.data.gEncoderCount_L)
                  2020000c    00000004     main.o (.data.gEncoderCount_R)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             454    51        0      
       main.o                         344    0         16     
       startup_mspm0g350x_ticlang.o   6      192       0      
    +--+------------------------------+------+---------+---------+
       Total:                         804    243       16     
                                                              
    .\Hardware\
       Motor.o                        92     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         92     0         0      
                                                              
    C:/ti/mspm0_sdk_2_04_00_06/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     356    0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o   288    0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         654    0         0      
                                                              
    C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       memcpy16.S.obj                 154    0         0      
       copy_decompress_lzss.c.obj     124    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         428    0         0      
                                                              
    C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       aeabi_memcpy.S.obj             8      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         8      0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      43        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   1990   286       528    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000008dc records: 2, size/record: 8, table size: 16
	.bss: load addr=000008cc, load size=00000008 bytes, run addr=20200000, run size=00000008 bytes, compression=zero_init
	.data: load addr=000008d4, load size=00000007 bytes, run addr=20200008, run size=00000008 bytes, compression=lzss


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000008c0 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
000003f7  ADC0_IRQHandler                      
000003f7  ADC1_IRQHandler                      
000003f7  AES_IRQHandler                       
0000087a  C$$EXIT                              
000003f7  CANFD0_IRQHandler                    
000003f7  DAC0_IRQHandler                      
00000861  DL_Common_delayCycles                
000001c5  DL_SYSCTL_configSYSPLL               
000006d5  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000000c1  DL_Timer_initFourCCPWMMode           
000007b1  DL_Timer_setCaptCompUpdateMethod     
000007e9  DL_Timer_setCaptureCompareOutCtl     
00000841  DL_Timer_setCaptureCompareValue      
000007cd  DL_Timer_setClockConfig              
000003f7  DMA_IRQHandler                       
000003f7  Default_Handler                      
000003f7  GROUP0_IRQHandler                    
000002a1  GROUP1_IRQHandler                    
0000087b  HOSTexit                             
000003f7  HardFault_Handler                    
000003f7  I2C0_IRQHandler                      
000003f7  I2C1_IRQHandler                      
000003f7  NMI_Handler                          
000003f7  PendSV_Handler                       
000003f7  RTC_IRQHandler                       
0000087f  Reset_Handler                        
000003f7  SPI0_IRQHandler                      
000003f7  SPI1_IRQHandler                      
000003f7  SVC_Handler                          
00000511  SYSCFG_DL_GPIO_init                  
000003f9  SYSCFG_DL_Motor_init                 
0000060d  SYSCFG_DL_SYSCTL_init                
0000086b  SYSCFG_DL_SYSTICK_init               
00000801  SYSCFG_DL_init                       
00000755  SYSCFG_DL_initPower                  
00000679  Set_PWM_Duty                         
00000851  SysTick_Handler                      
000003f7  TIMA0_IRQHandler                     
000003f7  TIMA1_IRQHandler                     
000003f7  TIMG0_IRQHandler                     
000003f7  TIMG12_IRQHandler                    
000003f7  TIMG6_IRQHandler                     
000003f7  TIMG7_IRQHandler                     
000003f7  TIMG8_IRQHandler                     
000003f7  UART0_IRQHandler                     
000003f7  UART1_IRQHandler                     
000003f7  UART2_IRQHandler                     
000003f7  UART3_IRQHandler                     
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
000008dc  __TI_CINIT_Base                      
000008ec  __TI_CINIT_Limit                     
000008ec  __TI_CINIT_Warm                      
000008c0  __TI_Handler_Table_Base              
000008cc  __TI_Handler_Table_Limit             
00000719  __TI_auto_init_nobinit_nopinit       
00000591  __TI_decompress_lzss                 
0000082f  __TI_decompress_none                 
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00000819  __TI_zero_init_nomemset              
0000086d  __aeabi_memcpy                       
0000086d  __aeabi_memcpy4                      
0000086d  __aeabi_memcpy8                      
ffffffff  __binit__                            
UNDEFED   __mpu_init                           
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00000789  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00000883  _system_pre_init                     
00000875  abort                                
ffffffff  binit                                
20200008  gEncoderCount_L                      
2020000c  gEncoderCount_R                      
20200000  gpioB                                
00000000  interruptVectors                     
00000485  main                                 
0000035d  memcpy                               
20200004  tick_ms                              


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  DL_Timer_initFourCCPWMMode           
000001c5  DL_SYSCTL_configSYSPLL               
00000200  __STACK_SIZE                         
000002a1  GROUP1_IRQHandler                    
0000035d  memcpy                               
000003f7  ADC0_IRQHandler                      
000003f7  ADC1_IRQHandler                      
000003f7  AES_IRQHandler                       
000003f7  CANFD0_IRQHandler                    
000003f7  DAC0_IRQHandler                      
000003f7  DMA_IRQHandler                       
000003f7  Default_Handler                      
000003f7  GROUP0_IRQHandler                    
000003f7  HardFault_Handler                    
000003f7  I2C0_IRQHandler                      
000003f7  I2C1_IRQHandler                      
000003f7  NMI_Handler                          
000003f7  PendSV_Handler                       
000003f7  RTC_IRQHandler                       
000003f7  SPI0_IRQHandler                      
000003f7  SPI1_IRQHandler                      
000003f7  SVC_Handler                          
000003f7  TIMA0_IRQHandler                     
000003f7  TIMA1_IRQHandler                     
000003f7  TIMG0_IRQHandler                     
000003f7  TIMG12_IRQHandler                    
000003f7  TIMG6_IRQHandler                     
000003f7  TIMG7_IRQHandler                     
000003f7  TIMG8_IRQHandler                     
000003f7  UART0_IRQHandler                     
000003f7  UART1_IRQHandler                     
000003f7  UART2_IRQHandler                     
000003f7  UART3_IRQHandler                     
000003f9  SYSCFG_DL_Motor_init                 
00000485  main                                 
00000511  SYSCFG_DL_GPIO_init                  
00000591  __TI_decompress_lzss                 
0000060d  SYSCFG_DL_SYSCTL_init                
00000679  Set_PWM_Duty                         
000006d5  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00000719  __TI_auto_init_nobinit_nopinit       
00000755  SYSCFG_DL_initPower                  
00000789  _c_int00_noargs                      
000007b1  DL_Timer_setCaptCompUpdateMethod     
000007cd  DL_Timer_setClockConfig              
000007e9  DL_Timer_setCaptureCompareOutCtl     
00000801  SYSCFG_DL_init                       
00000819  __TI_zero_init_nomemset              
0000082f  __TI_decompress_none                 
00000841  DL_Timer_setCaptureCompareValue      
00000851  SysTick_Handler                      
00000861  DL_Common_delayCycles                
0000086b  SYSCFG_DL_SYSTICK_init               
0000086d  __aeabi_memcpy                       
0000086d  __aeabi_memcpy4                      
0000086d  __aeabi_memcpy8                      
00000875  abort                                
0000087a  C$$EXIT                              
0000087b  HOSTexit                             
0000087f  Reset_Handler                        
00000883  _system_pre_init                     
000008c0  __TI_Handler_Table_Base              
000008cc  __TI_Handler_Table_Limit             
000008dc  __TI_CINIT_Base                      
000008ec  __TI_CINIT_Limit                     
000008ec  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200000  gpioB                                
20200004  tick_ms                              
20200008  gEncoderCount_L                      
2020000c  gEncoderCount_R                      
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[94 symbols]
