/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.22.0+3893"}
 */

/**
 * Import the modules used in this configuration.
 */
const GPIO    = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1   = GPIO.addInstance();
const GPIO2   = GPIO.addInstance();
const GPIO3   = GPIO.addInstance();
const PWM     = scripting.addModule("/ti/driverlib/PWM", {}, false);
const PWM1    = PWM.addInstance();
const SYSCTL  = scripting.addModule("/ti/driverlib/SYSCTL");
const SYSTICK = scripting.addModule("/ti/driverlib/SYSTICK");

/**
 * Write custom configuration values to the imported modules.
 */
const divider6       = system.clockTree["PLL_CLK2X_DIV"];
divider6.divideValue = 4;

const divider7       = system.clockTree["PLL_PDIV"];
divider7.divideValue = 2;

const divider9       = system.clockTree["UDIV"];
divider9.divideValue = 2;

const multiplier2         = system.clockTree["PLL_QDIV"];
multiplier2.multiplyValue = 10;

const mux8       = system.clockTree["HSCLKMUX"];
mux8.inputSelect = "HSCLKMUX_SYSPLL2X";

GPIO1.$name                          = "Motor_direction";
GPIO1.associatedPins.create(4);
GPIO1.associatedPins[0].$name        = "AIN1";
GPIO1.associatedPins[0].initialValue = "SET";
GPIO1.associatedPins[0].pin.$assign  = "PB0";
GPIO1.associatedPins[1].$name        = "AIN2";
GPIO1.associatedPins[1].initialValue = "SET";
GPIO1.associatedPins[1].pin.$assign  = "PB3";
GPIO1.associatedPins[2].$name        = "BIN1";
GPIO1.associatedPins[2].initialValue = "SET";
GPIO1.associatedPins[2].pin.$assign  = "PB1";
GPIO1.associatedPins[3].$name        = "BIN2";
GPIO1.associatedPins[3].initialValue = "SET";
GPIO1.associatedPins[3].pin.$assign  = "PB2";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

GPIO2.$name                               = "ECODER_R";
GPIO2.associatedPins.create(2);
GPIO2.associatedPins[0].direction         = "INPUT";
GPIO2.associatedPins[0].interruptEn       = true;
GPIO2.associatedPins[0].interruptPriority = "0";
GPIO2.associatedPins[0].polarity          = "RISE";
GPIO2.associatedPins[0].$name             = "LA";
GPIO2.associatedPins[0].pin.$assign       = "PB21";
GPIO2.associatedPins[1].direction         = "INPUT";
GPIO2.associatedPins[1].interruptPriority = "0";
GPIO2.associatedPins[1].polarity          = "RISE";
GPIO2.associatedPins[1].$name             = "LB";
GPIO2.associatedPins[1].interruptEn       = true;
GPIO2.associatedPins[1].pin.$assign       = "PB22";

GPIO3.$name                               = "ECODER_L";
GPIO3.associatedPins.create(2);
GPIO3.associatedPins[0].$name             = "RA";
GPIO3.associatedPins[0].direction         = "INPUT";
GPIO3.associatedPins[0].interruptEn       = true;
GPIO3.associatedPins[0].interruptPriority = "0";
GPIO3.associatedPins[0].polarity          = "RISE";
GPIO3.associatedPins[0].pin.$assign       = "PB15";
GPIO3.associatedPins[1].$name             = "RB";
GPIO3.associatedPins[1].direction         = "INPUT";
GPIO3.associatedPins[1].interruptEn       = true;
GPIO3.associatedPins[1].interruptPriority = "0";
GPIO3.associatedPins[1].polarity          = "RISE";
GPIO3.associatedPins[1].pin.$assign       = "PB16";

PWM1.timerStartTimer                    = true;
PWM1.clockPrescale                      = 100;
PWM1.timerCount                         = 400;
PWM1.$name                              = "Motor";
PWM1.pwmMode                            = "EDGE_ALIGN_UP";
PWM1.peripheral.$assign                 = "TIMG0";
PWM1.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC0";
PWM1.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC1";
PWM1.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric0";
PWM1.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric1";
PWM1.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");

SYSCTL.forceDefaultClkConfig = true;
SYSCTL.clockTreeEn           = true;

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
Board.peripheral.$suggestSolution          = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution = "PA20";
Board.peripheral.swdioPin.$suggestSolution = "PA19";
PWM1.peripheral.ccp0Pin.$suggestSolution   = "PA12";
PWM1.peripheral.ccp1Pin.$suggestSolution   = "PA13";
