#ifndef __PID_H_
#define __PID_H_
#include "main.h"


// PID控制器结构体
typedef struct {
	float Kp;           // 比例系数
	float Ki;           // 积分系数
	float Kd;           // 微分系数
	float target;       // 目标值
	float current;      // 当前值
	float error;        // 当前误差
	float integral;     // 积分值
	float last_error;   // 上一次误差
	float prev_error;   // 上上次误差
	float output;       // 输出值
} PID_TypeDef;

float PID_Calc(PID_TypeDef *pid);


#endif
