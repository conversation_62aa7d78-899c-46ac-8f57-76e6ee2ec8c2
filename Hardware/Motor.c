#include "Motor.h"


void Set_PWM_Duty(uint8_t channel, int32_t duty)
{
  uint16_t cmp_val = abs(duty) * 400 / 100;

  if (channel == 1) {
    if (duty >= 0) { // 正转
      DL_GPIO_setPins(GPIOB, DL_GPIO_PIN_3);    // AIN1 = 1
      DL_GPIO_clearPins(GPIOB, DL_GPIO_PIN_0);  // AIN2 = 0
    } else { // 反转
      DL_GPIO_setPins(GPIOB, DL_GPIO_PIN_0);  // AIN1 = 0
      DL_GPIO_clearPins(GPIOB, DL_GPIO_PIN_3);    // AIN2 = 1
    }
    DL_TimerG_setCaptureCompareValue(Motor_INST, cmp_val, GPIO_Motor_C0_IDX);
  }
  else if (channel == 0) {
    if (duty >= 0) { // 正转
      DL_GPIO_setPins(GPIOB, DL_GPIO_PIN_1);    // BIN1 = 1
      DL_GPIO_clearPins(GPIOB, DL_GPIO_PIN_2);  // BIN2 = 0
    } else { // 反转
      DL_GPIO_setPins(GPIOB, DL_GPIO_PIN_2);  // BIN1 = 0
      DL_GPIO_clearPins(GPIOB, DL_GPIO_PIN_1);    // BIN2 = 1
    }
    DL_TimerG_setCaptureCompareValue(Motor_INST, cmp_val, GPIO_Motor_C1_IDX);
  }
    
}