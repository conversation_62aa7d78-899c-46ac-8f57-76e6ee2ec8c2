#ifndef _PID_APP_H
#define _PID_APP_H

#include "main.h"
#include "pid.h"

// 电机通道定义
#define MOTOR_LEFT  0
#define MOTOR_RIGHT 1

// PID控制器实例声明
extern PID_TypeDef pid_left;   // 左轮PID控制器
extern PID_TypeDef pid_right;  // 右轮PID控制器

// PID应用层函数声明

/**
 * @brief PID控制器初始化
 * @param 无
 * @return 无
 */
void PID_App_Init(void);

/**
 * @brief 设置PID参数
 * @param motor 电机通道 (MOTOR_LEFT 或 MOTOR_RIGHT)
 * @param kp 比例系数
 * @param ki 积分系数
 * @param kd 微分系数
 * @return 无
 */
void PID_App_SetParams(uint8_t motor, float kp, float ki, float kd);

/**
 * @brief 设置目标速度
 * @param left_speed 左轮目标速度 (脉冲/秒)
 * @param right_speed 右轮目标速度 (脉冲/秒)
 * @return 无
 */
void PID_App_SetTarget(float left_speed, float right_speed);

/**
 * @brief PID控制循环 (每5ms调用一次)
 * @param 无
 * @return 无
 */
void PID_App_Control(void);

#endif