#include "pid.h"

float PID_Calc(PID_TypeDef *pid)
{
	// 计算误差
	pid->error = pid->target - pid->current;

	// 计算积分项（带积分限幅）
	pid->integral += pid->error;

	// 计算PID输出
	pid->output = pid->Kp * pid->error +                    // 比例项
				  pid->Ki * pid->integral +                 // 积分项
				  pid->Kd * (pid->error - pid->last_error); // 微分项

	// 更新误差记录
	pid->prev_error = pid->last_error;
	pid->last_error = pid->error;

	return pid->output;
}