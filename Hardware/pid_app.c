#include "pid_app.h"

// PID控制器实例定义
PID_TypeDef pid_left;   // 左轮PID控制器
PID_TypeDef pid_right;  // 右轮PID控制器

// 内部变量
static float left_target_speed = 0.0f;   // 左轮目标速度
static float right_target_speed = 0.0f;  // 右轮目标速度

/**
 * @brief PID控制器初始化
 * @param 无
 * @return 无
 */
void PID_App_Init(void)
{
    // 初始化左轮PID控制器
    pid_left.Kp = 1.0f;        // 比例系数
    pid_left.Ki = 0.1f;        // 积分系数
    pid_left.Kd = 0.05f;       // 微分系数
    pid_left.target = 0.0f;    // 目标值
    pid_left.current = 0.0f;   // 当前值
    pid_left.error = 0.0f;     // 当前误差
    pid_left.integral = 0.0f;  // 积分值
    pid_left.last_error = 0.0f;    // 上一次误差
    pid_left.prev_error = 0.0f;    // 上上次误差
    pid_left.output = 0.0f;    // 输出值

    // 初始化右轮PID控制器
    pid_right.Kp = 1.0f;       // 比例系数
    pid_right.Ki = 0.1f;       // 积分系数
    pid_right.Kd = 0.05f;      // 微分系数
    pid_right.target = 0.0f;   // 目标值
    pid_right.current = 0.0f;  // 当前值
    pid_right.error = 0.0f;    // 当前误差
    pid_right.integral = 0.0f; // 积分值
    pid_right.last_error = 0.0f;   // 上一次误差
    pid_right.prev_error = 0.0f;   // 上上次误差
    pid_right.output = 0.0f;   // 输出值

    // 初始化目标速度
    left_target_speed = 0.0f;
    right_target_speed = 0.0f;
}

/**
 * @brief 设置PID参数
 * @param motor 电机通道 (MOTOR_LEFT 或 MOTOR_RIGHT)
 * @param kp 比例系数
 * @param ki 积分系数
 * @param kd 微分系数
 * @return 无
 */
void PID_App_SetParams(uint8_t motor, float kp, float ki, float kd)
{
    if (motor == MOTOR_LEFT) {
        pid_left.Kp = kp;
        pid_left.Ki = ki;
        pid_left.Kd = kd;
    } else if (motor == MOTOR_RIGHT) {
        pid_right.Kp = kp;
        pid_right.Ki = ki;
        pid_right.Kd = kd;
    }
}

/**
 * @brief 设置目标速度
 * @param left_speed 左轮目标速度 (脉冲/秒)
 * @param right_speed 右轮目标速度 (脉冲/秒)
 * @return 无
 */
void PID_App_SetTarget(float left_speed, float right_speed)
{
    left_target_speed = left_speed;
    right_target_speed = right_speed;
    pid_left.target = left_speed;
    pid_right.target = right_speed;
}


/**
 * @brief 限制输出值到指定范围
 * @param value 输入值
 * @param min 最小值
 * @param max 最大值
 * @return 限制后的值
 */
static float limit_output(float value, float min, float max)
{
    if (value > max) {
        return max;
    } else if (value < min) {
        return min;
    }
    return value;
}

/**
 * @brief PID控制循环 (每5ms调用一次)
 * @param 无
 * @return 无
 */
void PID_App_Control(void)
{
    // 计算当前速度 (编码器计数 * 200 = 脉冲/秒)
    // 5ms周期，所以 1000ms/5ms = 200
    float left_current_speed = (float)gEncoderCount_L * 200.0f;
    float right_current_speed = (float)gEncoderCount_R * 200.0f;

    // 更新PID控制器的当前值
    pid_left.current = left_current_speed;
    pid_right.current = right_current_speed;

    // 计算PID输出
    float left_output = PID_Calc(&pid_left);
    float right_output = PID_Calc(&pid_right);

    // 限制输出范围到-100至100 (PWM占空比范围)
    left_output = limit_output(left_output, -100.0f, 100.0f);
    right_output = limit_output(right_output, -100.0f, 100.0f);

    // 设置电机PWM
    Set_PWM_Duty(MOTOR_LEFT, (int32_t)left_output);   // 左轮
    Set_PWM_Duty(MOTOR_RIGHT, (int32_t)right_output); // 右轮
}