#include "main.h"



void SysTick_Init(void)
{
    DL_SYSTICK_config(CPUCLK_FREQ/1000);
    NVIC_SetPriority(SysTick_IRQn, 0);
}

int main(void)
{
    SYSCFG_DL_init();
    SysTick_Init();
    NVIC_EnableIRQ(GPIOB_INT_IRQn); // 编码器外部中断

    // 初始化PID控制器
    PID_App_Init();

    Set_PWM_Duty(0, 50);
    // // 设置测试目标速度 (可选)
    // PID_App_SetTarget(100.0f, 100.0f); // 左右轮目标速度100脉冲/秒
    DL_GPIO_setPins(GPIOB, DL_GPIO_PIN_3);
    while (1)
    {
      if (tick_ms>=5) {
        tick_ms=0;

        // // 在编码器计数清零前执行PID控制
        // PID_App_Control();

        // 清零编码器计数
        gEncoderCount_L=gEncoderCount_R=0;
      }
    }
}

//编码器相关变量

volatile uint32_t gpioB;
int32_t gEncoderCount_L = 0;
int32_t gEncoderCount_R = 0;

void GROUP1_IRQHandler(void) {
  // 获取中断信号
  gpioB = DL_GPIO_getEnabledInterruptStatus(GPIOB, DL_GPIO_PIN_15|DL_GPIO_PIN_16|DL_GPIO_PIN_21|DL_GPIO_PIN_22);
  
  // 左轮
  if (gpioB & DL_GPIO_PIN_15) {
    if (!DL_GPIO_readPins(GPIOB, DL_GPIO_PIN_16)) {
      gEncoderCount_L++;
    } else {
      gEncoderCount_L--;
    }
    DL_GPIO_clearInterruptStatus(GPIOB, DL_GPIO_PIN_15);
  }
  if (gpioB & DL_GPIO_PIN_16) {
    if (!DL_GPIO_readPins(GPIOB, DL_GPIO_PIN_15)) {
      gEncoderCount_L--;
    } else {
      gEncoderCount_L++;
    }
    DL_GPIO_clearInterruptStatus(GPIOB, DL_GPIO_PIN_16);
  }

  
  if (gpioB & DL_GPIO_PIN_21) {
    if (!DL_GPIO_readPins(GPIOB, DL_GPIO_PIN_22)) {
      gEncoderCount_R--;
      
    } else {
      gEncoderCount_R++;
    }
    DL_GPIO_clearInterruptStatus(GPIOB, DL_GPIO_PIN_21);
  }
    if (gpioB & DL_GPIO_PIN_22) {
    if (!DL_GPIO_readPins(GPIOB, DL_GPIO_PIN_21)) {
      gEncoderCount_R++;
      
    } else {
      gEncoderCount_R--;
    }
    DL_GPIO_clearInterruptStatus(GPIOB, DL_GPIO_PIN_22);
  }
}



volatile unsigned long tick_ms;

void SysTick_Handler(void){
    tick_ms++;
}
